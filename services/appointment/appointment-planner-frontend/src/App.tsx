import React from 'react';
import { useRoutes } from 'react-router-dom';
import { AppointmentCalendar } from './components/AppointmentCalendar';
import { AppointmentForm } from './components/AppointmentForm';

const AppRoutes = () => {
  return useRoutes([
    {
      path: '/',
      element: <AppointmentCalendar salonId="default" />
    },
    {
      path: '/new',
      element: <AppointmentForm />
    },
    {
      path: '/edit/:id',
      element: <AppointmentForm />
    }
  ]);
};

function App() {
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">
          Beauty CRM - Appointment Planner
        </h1>
        <AppRoutes />
      </div>
    </div>
  );
}

export default App;
