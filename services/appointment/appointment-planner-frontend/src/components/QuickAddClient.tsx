import React, { useState } from 'react';
import { QuickClient } from '../types/calendar';

interface QuickAddClientProps {
  onClientAdded: (client: QuickClient) => void;
  onCancel: () => void;
}

export const QuickAddClient: React.FC<QuickAddClientProps> = ({
  onClientAdded,
  onCancel
}) => {
  const [client, setClient] = useState<QuickClient>({
    name: '',
    phone: '',
    email: ''
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (client.name && client.phone) {
      onClientAdded(client);
      setClient({ name: '', phone: '', email: '' });
    }
  };

  return (
    <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
      <h3 className="text-lg font-semibold mb-4">Quick Add Client</h3>
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label htmlFor="client-name" className="block text-sm font-medium text-gray-700">
            Client Name *
          </label>
          <input
            id="client-name"
            type="text"
            value={client.name}
            onChange={(e) => setClient({ ...client, name: e.target.value })}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            placeholder="Enter client name"
            required
          />
        </div>

        <div>
          <label htmlFor="client-phone" className="block text-sm font-medium text-gray-700">
            Phone Number *
          </label>
          <input
            id="client-phone"
            type="tel"
            value={client.phone}
            onChange={(e) => setClient({ ...client, phone: e.target.value })}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            placeholder="(*************"
            required
          />
        </div>

        <div>
          <label htmlFor="client-email" className="block text-sm font-medium text-gray-700">
            Email (Optional)
          </label>
          <input
            id="client-email"
            type="email"
            value={client.email}
            onChange={(e) => setClient({ ...client, email: e.target.value })}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            placeholder="<EMAIL>"
          />
        </div>

        <div className="flex space-x-3">
          <button
            type="submit"
            className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          >
            Add Client
          </button>
          <button
            type="button"
            onClick={onCancel}
            className="flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
          >
            Cancel
          </button>
        </div>
      </form>
    </div>
  );
};
