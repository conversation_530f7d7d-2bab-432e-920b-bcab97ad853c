import React, { useEffect } from 'react';

// Accessibility enhancement hook
export const useAccessibilityEnhancements = () => {
  useEffect(() => {
    // Add ARIA labels to calendar navigation
    const addAriaLabels = () => {
      const prevButton = document.querySelector('.fc-prev-button');
      const nextButton = document.querySelector('.fc-next-button');
      const todayButton = document.querySelector('.fc-today-button');

      if (prevButton) {
        prevButton.setAttribute('aria-label', 'Previous month');
      }
      if (nextButton) {
        nextButton.setAttribute('aria-label', 'Next month');
      }
      if (todayButton) {
        todayButton.setAttribute('aria-label', 'Go to today');
      }
    };

    // Add keyboard navigation for appointment slots
    const addKeyboardNavigation = () => {
      const slots = document.querySelectorAll('.fc-timegrid-slot');
      slots.forEach((slot, index) => {
        slot.setAttribute('tabindex', '0');
        slot.setAttribute('role', 'button');
        slot.setAttribute('aria-label', `Time slot ${index + 1}`);

        slot.addEventListener('keydown', (event) => {
          if (event.key === 'Enter' || event.key === ' ') {
            event.preventDefault();
            slot.click();
          }
        });
      });
    };

    // Initialize accessibility features
    const timer = setTimeout(() => {
      addAriaLabels();
      addKeyboardNavigation();
    }, 100);

    return () => clearTimeout(timer);
  }, []);
};

// Screen reader announcements
export const announceToScreenReader = (message: string) => {
  const announcement = document.createElement('div');
  announcement.setAttribute('aria-live', 'polite');
  announcement.setAttribute('aria-atomic', 'true');
  announcement.className = 'sr-only';
  announcement.textContent = message;

  document.body.appendChild(announcement);

  setTimeout(() => {
    document.body.removeChild(announcement);
  }, 1000);
};
