import { useMemo, useCallback, useState, useEffect } from 'react';
import type { CalendarEvent, Appointment } from '../types/calendar';

// Memoized calendar event transformation
export const useOptimizedCalendarEvents = (appointments: Appointment[]) => {
  return useMemo(() => {
    return appointments.map(
      (appointment): CalendarEvent => ({
        id: appointment.id,
        title: `${appointment.extendedProps?.clientName || 'Client'} - ${appointment.extendedProps?.serviceType || 'Service'}`,
        start: appointment.startTime,
        end: appointment.endTime,
        backgroundColor: getStatusColor(appointment.status),
        extendedProps: {
          clientName: appointment.extendedProps?.clientName,
          staffName: appointment.extendedProps?.staffName,
          serviceType: appointment.extendedProps?.serviceType,
          status: appointment.status,
          duration: appointment.extendedProps?.duration,
        },
        classNames: [`status-${appointment.status}`],
      }),
    );
  }, [appointments]);
};

// Optimized status color calculation
const getStatusColor = (status: string): string => {
  const colorMap: Record<string, string> = {
    confirmed: '#10b981',
    pending: '#f59e0b',
    cancelled: '#ef4444',
    completed: '#3b82f6',
  };
  return colorMap[status] || '#9e9e9e';
};

// Virtual scrolling for large appointment lists
export const useVirtualizedAppointments = (
  appointments: Appointment[],
  itemHeight = 80,
) => {
  const [visibleRange, setVisibleRange] = useState({ start: 0, end: 10 });
  const [containerHeight, setContainerHeight] = useState(400);

  const visibleAppointments = useMemo(() => {
    return appointments.slice(visibleRange.start, visibleRange.end);
  }, [appointments, visibleRange]);

  const handleScroll = useCallback(
    (scrollTop: number) => {
      const start = Math.floor(scrollTop / itemHeight);
      const visibleCount = Math.ceil(containerHeight / itemHeight);
      const end = Math.min(start + visibleCount + 5, appointments.length); // Buffer of 5 items

      setVisibleRange({ start, end });
    },
    [itemHeight, containerHeight, appointments.length],
  );

  return {
    visibleAppointments,
    totalHeight: appointments.length * itemHeight,
    handleScroll,
    setContainerHeight,
  };
};

// Debounced search for better performance
export const useDebouncedSearch = (searchTerm: string, delay = 300) => {
  const [debouncedTerm, setDebouncedTerm] = useState(searchTerm);

  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedTerm(searchTerm);
    }, delay);

    return () => clearTimeout(timer);
  }, [searchTerm, delay]);

  return debouncedTerm;
};

// Optimized event height calculation
export const useEventHeightCalculation = () => {
  return useCallback((duration: number): number => {
    // 60px per hour, minimum 30px
    return Math.max((duration / 60) * 60, 30);
  }, []);
};

// Memoized filter functions
export const useAppointmentFilters = () => {
  const filterByStatus = useCallback(
    (appointments: Appointment[], status: string) => {
      return appointments.filter((apt) => apt.status === status);
    },
    [],
  );

  const filterByDateRange = useCallback(
    (appointments: Appointment[], startDate: Date, endDate: Date) => {
      return appointments.filter(
        (apt) => apt.startTime >= startDate && apt.startTime <= endDate,
      );
    },
    [],
  );

  const filterByStaff = useCallback(
    (appointments: Appointment[], staffId: string) => {
      return appointments.filter((apt) => apt.staffId === staffId);
    },
    [],
  );

  return { filterByStatus, filterByDateRange, filterByStaff };
};
