import { EventInput } from '@fullcalendar/core';

// Fixed CalendarEvent interface to match FullCalendar API
export interface CalendarEvent extends EventInput {
  id: string;
  title: string;
  start: string | Date;
  end?: string | Date;
  allDay?: boolean;
  extendedProps?: {
    clientName?: string;
    clientPhone?: string;
    staffName?: string;
    serviceType?: string;
    duration?: number;
    status?: 'confirmed' | 'pending' | 'cancelled' | 'completed';
    notes?: string;
  };
  backgroundColor?: string;
  borderColor?: string;
  textColor?: string;
  classNames?: string[];
}

// Appointment data structure
export interface Appointment {
  id: string;
  clientId: string;
  staffId: string;
  serviceIds: string[];
  startTime: Date;
  endTime: Date;
  status: 'confirmed' | 'pending' | 'cancelled' | 'completed';
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

// Service bundling interface
export interface ServiceBundle {
  id: string;
  name: string;
  services: Service[];
  totalDuration: number;
  totalPrice: number;
  discount?: number;
}

export interface Service {
  id: string;
  name: string;
  duration: number;
  price: number;
  category: string;
}

// Quick-add client interface
export interface QuickClient {
  name: string;
  phone: string;
  email?: string;
}
