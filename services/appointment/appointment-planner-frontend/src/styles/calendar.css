/* Beauty CRM Calendar Styles */

/* FullCalendar Base Styling */
.fc {
  font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
}

.fc-theme-standard .fc-scrollgrid {
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
}

/* Event Styling */
.fc-event {
  border-radius: 8px;
  border: none;
  padding: 4px 8px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.fc-event:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Status-based Event Colors */
.fc-event.status-confirmed {
  background-color: #10b981;
  border-color: #059669;
  color: white;
}

.fc-event.status-pending {
  background-color: #f59e0b;
  border-color: #d97706;
  color: white;
}

.fc-event.status-cancelled {
  background-color: #ef4444;
  border-color: #dc2626;
  color: white;
}

.fc-event.status-completed {
  background-color: #3b82f6;
  border-color: #2563eb;
  color: white;
}

/* Time Slot Styling */
.fc-timegrid-slot {
  border-bottom: 1px solid #f3f4f6;
}

.fc-timegrid-slot.available-slot {
  background-color: #ecfdf5;
  border-left: 3px solid #10b981;
}

.fc-timegrid-slot.booked-slot {
  background-color: #fef2f2;
  border-left: 3px solid #ef4444;
}

/* Day View Enhancements */
.fc-timegrid-event {
  border-radius: 6px;
  margin: 1px;
}

.fc-timegrid-event .fc-event-main {
  padding: 4px 6px;
}

/* Week View Styling */
.fc-col-header-cell {
  background-color: #f9fafb;
  font-weight: 600;
  color: #374151;
  border-bottom: 2px solid #e5e7eb;
}

/* Month View Optimizations */
.fc-daygrid-event {
  border-radius: 4px;
  margin: 1px 2px;
  font-size: 0.75rem;
}

.fc-daygrid-event-harness {
  margin-bottom: 2px;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
  .fc-toolbar {
    flex-direction: column;
    gap: 0.5rem;
  }

  .fc-toolbar-chunk {
    display: flex;
    justify-content: center;
  }

  .fc-button-group {
    display: flex;
  }

  .fc-event {
    font-size: 0.75rem;
    padding: 2px 4px;
  }
}

/* Loading States */
.fc-loading {
  position: relative;
}

.fc-loading::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 32px;
  height: 32px;
  margin: -16px 0 0 -16px;
  border: 3px solid #e5e7eb;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Accessibility Enhancements */
.fc-event:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.fc-button:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Custom Tooltip Styling */
.appointment-tooltip {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  font-size: 0.875rem;
  max-width: 250px;
  z-index: 1000;
}

.appointment-tooltip h4 {
  font-weight: 600;
  margin-bottom: 4px;
  color: #111827;
}

.appointment-tooltip p {
  margin: 2px 0;
  color: #6b7280;
}
