/* Mobile Responsiveness Fixes for Beauty CRM Planner */

/* Base mobile styles */
@media (max-width: 768px) {
  .calendar-container {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  /* Fix overlapping UI elements */
  .grid-cols-1.lg\\:grid-cols-2 {
    grid-template-columns: 1fr !important;
    gap: 1rem;
  }

  /* Modal z-index fixes */
  .appointment-modal {
    z-index: 9999 !important;
    position: fixed !important;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
  }

  .appointment-modal .modal-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 90%;
    max-width: 400px;
    background: white;
    border-radius: 8px;
    padding: 1rem;
    max-height: 80vh;
    overflow-y: auto;
  }

  /* Touch-friendly buttons */
  .fc-button {
    min-height: 44px;
    min-width: 44px;
    padding: 8px 12px;
  }

  /* Improved calendar navigation */
  .fc-toolbar {
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .fc-toolbar-chunk {
    flex: 1;
    min-width: 0;
  }
}

/* Small mobile devices */
@media (max-width: 480px) {
  .fc-toolbar-title {
    font-size: 1.25rem;
  }

  .fc-button {
    font-size: 0.875rem;
    padding: 6px 10px;
  }

  .fc-event {
    font-size: 0.75rem;
    padding: 2px 4px;
  }
}
