#!/bin/bash

# Function to display help message
function show_help() {
  echo "Usage: $0 [options]"
  echo "Manage Docker containers for the Beauty CRM project"
  echo ""
  echo "Options:"
  echo "  --up                  Start all Docker containers"
  echo "  --down                Stop all Docker containers"
  echo "  --identity-up         Start identity services"
  echo "  --identity-down       Stop identity services"
  echo "  --elk-up              Start ELK stack"
  echo "  --elk-down            Stop ELK stack"
  echo "  --check               Check if Docker is running"
  echo "  --help                Display this help message"
}

# Default values
ACTION_UP=false
ACTION_DOWN=false
ACTION_IDENTITY_UP=false
ACTION_IDENTITY_DOWN=false
ACTION_ELK_UP=false
ACTION_ELK_DOWN=false
ACTION_CHECK=false

# Parse arguments
if [ $# -eq 0 ]; then
  show_help
  exit 1
fi

for arg in "$@"; do
  case $arg in
    --up)
      ACTION_UP=true
      ;;
    --down)
      ACTION_DOWN=true
      ;;
    --identity-up)
      ACTION_IDENTITY_UP=true
      ;;
    --identity-down)
      ACTION_IDENTITY_DOWN=true
      ;;
    --elk-up)
      ACTION_ELK_UP=true
      ;;
    --elk-down)
      ACTION_ELK_DOWN=true
      ;;
    --check)
      ACTION_CHECK=true
      ;;
    --help)
      show_help
      exit 0
      ;;
    *)
      echo "Unknown option: $arg"
      show_help
      exit 1
      ;;
  esac
done

# Check if Docker is running
function check_docker() {
  echo "🐳 Checking if Docker is running..."
  if ! docker info > /dev/null 2>&1; then
    echo "🚫 Docker is not running. Starting Docker..."
    open -a Docker
    echo "⏳ Waiting for Docker to start..."
    sleep 10
    if ! docker info > /dev/null 2>&1; then
      echo "❌ Failed to start Docker"
      exit 1
    fi
    echo "✅ Docker is ready!"
  else
    echo "✅ Docker is already running"
  fi
}

# Start Docker containers
function start_docker() {
  check_docker
  echo "🚀 Starting Docker containers..."
  docker compose up -d
  echo "✅ Docker containers are ready!"
}

# Stop Docker containers
function stop_docker() {
  echo "🛑 Stopping Docker containers..."
  docker compose down
  echo "✅ Docker containers stopped"
}

# Start identity services
function start_identity() {
  check_docker
  echo "🚀 Starting identity services..."
  docker compose -f identity-compose.yml up -d
  echo "✅ Identity services ready!"
}

# Stop identity services
function stop_identity() {
  echo "🛑 Stopping identity services..."
  docker compose -f identity-compose.yml down
  echo "✅ Identity services stopped"
}

# Start ELK stack
function start_elk() {
  check_docker
  echo "🚀 Starting ELK stack..."
  docker compose -f elk-compose.yml up -d
  echo "✅ ELK stack ready!"
}

# Stop ELK stack
function stop_elk() {
  echo "🛑 Stopping ELK stack..."
  docker compose -f elk-compose.yml down
  echo "✅ ELK stack stopped"
}

# Execute actions based on flags
if [[ "$ACTION_CHECK" == "true" ]]; then
  check_docker
fi

if [[ "$ACTION_UP" == "true" ]]; then
  start_docker
fi

if [[ "$ACTION_DOWN" == "true" ]]; then
  stop_docker
fi

if [[ "$ACTION_IDENTITY_UP" == "true" ]]; then
  start_identity
fi

if [[ "$ACTION_IDENTITY_DOWN" == "true" ]]; then
  stop_identity
fi

if [[ "$ACTION_ELK_UP" == "true" ]]; then
  start_elk
fi

if [[ "$ACTION_ELK_DOWN" == "true" ]]; then
  stop_elk
fi

# If both up and down flags are specified for any service, warn the user
if [[ "$ACTION_UP" == "true" && "$ACTION_DOWN" == "true" ]]; then
  echo "⚠️ Warning: Both --up and --down specified. Executing --up first, then --down."
fi

if [[ "$ACTION_IDENTITY_UP" == "true" && "$ACTION_IDENTITY_DOWN" == "true" ]]; then
  echo "⚠️ Warning: Both --identity-up and --identity-down specified. Executing --identity-up first, then --identity-down."
fi

if [[ "$ACTION_ELK_UP" == "true" && "$ACTION_ELK_DOWN" == "true" ]]; then
  echo "⚠️ Warning: Both --elk-up and --elk-down specified. Executing --elk-up first, then --elk-down."
fi 