#!/bin/bash

# Disable NX daemon immediately
export NX_NO_DAEMON=true
export NX_DAEMON_PORT=0
export NX_SKIP_NX_CACHE=true
export NX_IGNORE_CYCLES=true
export NX_PREFER_WASM=true

# Kill any running NX daemon processes
pkill -f "nx-daemon" || true

# Function to display help message
function show_help() {
  echo "Usage: $0 [options]"
  echo "Start services for the Beauty CRM project"
  echo ""
  echo "Options:"
  echo "  --all                Start all services"
  echo "  --minimal            Start minimal set of services"
  echo "  --salon              Start salon services"
  echo "  --inventory          Start inventory services"
  echo "  --appointment         Start appointment services"
  echo "  --crm                Start CRM services"
  echo "  --workflow           Start workflow services"
  echo "  --dashboard          Start dashboard services"
  echo "  --appointment-planner   Start appointment planner"
  echo "  --backend            Start all backend services"
  echo "  --frontend           Start all frontend services"
  echo "  --docker             Start required Docker containers"
  echo "  --shell              Start shell with remotes"
  echo "  --help               Display this help message"
}

# Default values
START_ALL=false
START_MINIMAL=false
START_SALON=false
START_INVENTORY=false
START_SCHEDULING=false
START_CRM=false
START_WORKFLOW=false
START_DASHBOARD=false
START_APPOINTMENT_PLANNER=false
START_BACKEND=false
START_FRONTEND=false
START_DOCKER=false
START_SHELL=false

# Parse arguments
if [ $# -eq 0 ]; then
  START_MINIMAL=true
fi

for arg in "$@"; do
  case $arg in
    --all)
      START_ALL=true
      ;;
    --minimal)
      START_MINIMAL=true
      ;;
    --salon)
      START_SALON=true
      ;;
    --inventory)
      START_INVENTORY=true
      ;;
    --appointment)
      START_SCHEDULING=true
      ;;
    --crm)
      START_CRM=true
      ;;
    --workflow)
      START_WORKFLOW=true
      ;;
    --dashboard)
      START_DASHBOARD=true
      ;;
    --appointment-planner)
      START_APPOINTMENT_PLANNER=true
      ;;
    --backend)
      START_BACKEND=true
      ;;
    --frontend)
      START_FRONTEND=true
      ;;
    --docker)
      START_DOCKER=true
      ;;
    --shell)
      START_SHELL=true
      ;;
    --help)
      show_help
      exit 0
      ;;
    *)
      echo "Unknown option: $arg"
      show_help
      exit 1
      ;;
  esac
done

# Check if Docker is running
function check_docker() {
  echo "🐳 Checking if Docker is running..."
  if ! docker info > /dev/null 2>&1; then
    echo "🚫 Docker is not running. Starting Docker..."
    open -a Docker
    echo "⏳ Waiting for Docker to start..."
    sleep 10
    if ! docker info > /dev/null 2>&1; then
      echo "❌ Failed to start Docker"
      exit 1
    fi
    echo "✅ Docker is ready!"
  else
    echo "✅ Docker is already running"
  fi
}

# Start Docker containers
function start_docker() {
  check_docker
  echo "🚀 Starting Docker containers..."
  docker compose up -d
  echo "✅ Docker containers are ready!"
}

# Kill running processes on ports
function kill_ports() {
  echo "🔪 Forcefully terminating application processes..."
  
  # Kill NX daemon and related processes
  echo "Killing NX processes..."
  pkill -9 -f 'nx/src/daemon' || true
  pkill -9 -f 'nx/bin' || true
  pkill -9 -f 'run-executor.js' || true
  
  # Kill all Node.js and Bun processes related to our services
  echo "Killing application processes..."
  # Only kill processes that are in our project directory
  pkill -9 -f 'beauty-crm.*vite' || true
  pkill -9 -f 'beauty-crm.*bun run dev' || true
  pkill -9 -f 'beauty-crm.*bun src' || true
  pkill -9 -f 'beauty-crm.*tsx watch' || true
  pkill -9 -f 'beauty-crm.*nx run' || true
  
  # Kill processes on specific ports only if they're Node, Bun or npm processes
  echo "Killing app processes on specific ports..."
  for port in 3001 3002 3003 3004 5001 5002 5003 5004; do
    pid=$(lsof -i:$port -t 2>/dev/null)
    if [ ! -z "$pid" ]; then
      # Check if it's a Node.js, Bun, or npm process before killing
      if ps -p $pid -o command | grep -q -e "node" -e "bun" -e "npm"; then
        echo "Killing process on port $port (PID: $pid)..."
        kill -9 $pid 2>/dev/null || true
      else
        echo "Skipping non-app process on port $port (PID: $pid)"
      fi
    fi
  done
  
  # Wait a moment to ensure all processes are terminated
  sleep 1
  
  echo "✅ Application processes terminated"
}

# Prepare environment
function prepare_environment() {
  kill_ports
  
  # Only build backend if we're starting something that needs it
  if [[ "$START_ALL" == "true" || "$START_BACKEND" == "true" || "$START_SALON" == "true" || "$START_INVENTORY" == "true" || "$START_SCHEDULING" == "true" || "$START_CRM" == "true" || "$START_WORKFLOW" == "true" || "$START_MINIMAL" == "true" || "$START_SHELL" == "true" ]]; then
    echo "🔧 Building backend..."
    bun run build:backend
    echo "🔧 Generating backend configs..."
    bun run generate:backend-configs
  fi
}

# Function to start all services
function start_all_services() {
  check_docker
  prepare_environment
  echo "🚀 Starting all services..."
  # Start backends with skipLibCheck
  NX_NO_DAEMON=true NX_DAEMON_PORT=0 nx run-many --target=dev --projects=crm-management-backend,inventory-management-backend,appointment-management-backend,appointment-worker-backend,tenant-management-backend,workflow-management-backend --parallel=6 --ignore-cycles -- --skipLibCheck &
  
  # Start frontends without skipLibCheck (Vite doesn't support it)
  NX_NO_DAEMON=true NX_DAEMON_PORT=0 nx run-many --target=dev --projects=beauty-crm-shell,crm-management-frontend,dashboard-management-frontend,identity-management-frontend,inventory-management-frontend,appointment-management-frontend,tenant-management-frontend,workflow-management-frontend --parallel=8 --ignore-cycles &
  
  wait
}

# Function to start minimal services
function start_minimal_services() {
  check_docker
  prepare_environment
  echo "🚀 Starting minimal services..."
  # Run backend with Bun on specific ports
  NX_NO_DAEMON=true NX_DAEMON_PORT=0 nx run @beauty-crm/salon-management-backend:dev -- --bunx "bun run dev" --port=5021 &
  # Run another backend with Bun on a different port
  NX_NO_DAEMON=true NX_DAEMON_PORT=0 nx run @beauty-crm/appointment-planner-backend:dev -- --bunx "bun src/index.ts" --port=5016 &
  # Run frontend on a specific port
  NX_NO_DAEMON=true NX_DAEMON_PORT=0 nx run @beauty-crm/appointment-planner-frontend:dev -- --port=5015 &
  wait
}

# Function to start salon services
function start_salon_services() {
  check_docker
  prepare_environment
  echo "🚀 Starting salon services..."
  echo "Generating Prisma client for salon service..."
  cd services/salon/salon-management-backend && npx prisma generate && cd ../../..
  # Backend with Bun 
  NX_NO_DAEMON=true NX_DAEMON_PORT=0 nx run salon-management-backend:dev -- --bunx "bun src/index.ts" --port=3005 & 
  # Frontend without skipLibCheck
  NX_NO_DAEMON=true NX_DAEMON_PORT=0 nx run salon-management-frontend:dev &
  wait
}

# Function to start inventory services
function start_inventory_services() {
  check_docker
  prepare_environment
  echo "🚀 Starting inventory services..."
  # Backend with Bun
  NX_NO_DAEMON=true NX_DAEMON_PORT=0 nx run inventory-management-backend:dev -- --bunx "bun src/index.ts" &
  # Frontend without skipLibCheck
  NX_NO_DAEMON=true NX_DAEMON_PORT=0 nx run inventory-management-frontend:dev &
  wait
}

# Function to start appointment services
function start_appointment_services() {
  check_docker
  prepare_environment
  echo "🚀 Starting appointment services..."
  # Backend with Bun
  NX_NO_DAEMON=true NX_DAEMON_PORT=0 nx run appointment-management-backend:dev -- --bunx "bun src/index.ts" &
  # Frontend without skipLibCheck
  NX_NO_DAEMON=true NX_DAEMON_PORT=0 nx run appointment-management-frontend:dev &
  wait
}

# Function to start CRM services
function start_crm_services() {
  check_docker
  prepare_environment
  echo "🚀 Starting CRM services..."
  # Backend with Bun
  NX_NO_DAEMON=true NX_DAEMON_PORT=0 nx run crm-management-backend:dev -- --bunx "bun src/index.ts" &
  # Frontend without skipLibCheck
  NX_NO_DAEMON=true NX_DAEMON_PORT=0 nx run crm-management-frontend:dev &
  wait
}

# Function to start workflow services
function start_workflow_services() {
  check_docker
  prepare_environment
  echo "🚀 Starting workflow services..."
  # Backend with Bun
  NX_NO_DAEMON=true NX_DAEMON_PORT=0 nx run workflow-management-backend:dev -- --bunx "bun src/index.ts" &
  # Frontend without skipLibCheck
  NX_NO_DAEMON=true NX_DAEMON_PORT=0 nx run workflow-management-frontend:dev &
  wait
}

# Function to start dashboard services
function start_dashboard_services() {
  check_docker
  prepare_environment
  echo "🚀 Starting dashboard services..."
  # Frontend only - no skipLibCheck
  NX_NO_DAEMON=true NX_DAEMON_PORT=0 nx run dashboard-management-frontend:dev
}

# Function to start appointment planner
function start_appointment_planner() {
  check_docker
  prepare_environment
  echo "🚀 Starting appointment planner..."
  # Run backend with Bun
  NX_NO_DAEMON=true NX_DAEMON_PORT=0 nx run @beauty-crm/appointment-planner-backend:dev -- --bunx "bun src/index.ts" &
  # Run frontend without skipLibCheck (Vite doesn't support it)
  NX_NO_DAEMON=true NX_DAEMON_PORT=0 nx run @beauty-crm/appointment-planner-frontend:dev &
  wait
}

# Function to start all backend services
function start_backend_services() {
  check_docker
  prepare_environment
  echo "🚀 Starting all backend services..."
  echo "Using Bun for all backend services for better performance..."
  NX_NO_DAEMON=true NX_DAEMON_PORT=0 nx run-many --target=dev --projects=crm-management-backend,inventory-management-backend,appointment-management-backend,appointment-worker-backend,tenant-management-backend,workflow-management-backend --parallel=6 --ignore-cycles -- --bunx "bun run dev"
}

# Function to start all frontend services
function start_frontend_services() {
  check_docker
  prepare_environment
  echo "🚀 Starting all frontend services..."
  # Frontend services shouldn't use --skipLibCheck as it's not supported by Vite
  NX_NO_DAEMON=true NX_DAEMON_PORT=0 nx run-many --target=dev --projects=beauty-crm-shell,crm-management-frontend,dashboard-management-frontend,identity-management-frontend,inventory-management-frontend,appointment-management-frontend,tenant-management-frontend,workflow-management-frontend --parallel=8 --ignore-cycles
}

# Function to start shell with remotes
function start_shell() {
  check_docker
  prepare_environment
  echo "🚀 Starting shell with remotes..."
  concurrently "cd services/orchestration/beauty-crm-shell && platform-shell-lifecycle start --app-type shell" "cd services/inventory/inventory-management-frontend && platform-shell-lifecycle start --app-type remote" "cd services/appointment/appointment-management-frontend && platform-shell-lifecycle start --app-type remote" "cd services/orchestration/dashboard-management-frontend && platform-shell-lifecycle start --app-type remote" "bun run start:identity" "bun run start:backend"
}

# Execute starts based on flags
if [[ "$START_DOCKER" == "true" ]]; then
  start_docker
fi

if [[ "$START_ALL" == "true" ]]; then
  start_all_services
  exit 0
fi

if [[ "$START_MINIMAL" == "true" ]]; then
  start_minimal_services
  exit 0
fi

if [[ "$START_SHELL" == "true" ]]; then
  start_shell
  exit 0
fi

if [[ "$START_BACKEND" == "true" ]]; then
  start_backend_services
fi

if [[ "$START_FRONTEND" == "true" ]]; then
  start_frontend_services
fi

if [[ "$START_SALON" == "true" ]]; then
  start_salon_services
fi

if [[ "$START_INVENTORY" == "true" ]]; then
  start_inventory_services
fi

if [[ "$START_SCHEDULING" == "true" ]]; then
  start_appointment_services
fi

if [[ "$START_CRM" == "true" ]]; then
  start_crm_services
fi

if [[ "$START_WORKFLOW" == "true" ]]; then
  start_workflow_services
fi

if [[ "$START_DASHBOARD" == "true" ]]; then
  start_dashboard_services
fi

if [[ "$START_APPOINTMENT_PLANNER" == "true" ]]; then
  start_appointment_planner
fi

echo "✅ Services started!" 