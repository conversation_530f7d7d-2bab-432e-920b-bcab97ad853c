#!/bin/bash

# Simple script to activate the workflow directly
set -e

echo "🔧 Activating n8n workflow directly..."

# Check if n8n is running
if ! curl -s http://localhost:5678/healthz > /dev/null 2>&1; then
    echo "❌ n8n is not running"
    exit 1
fi

echo "✅ n8n is running"

# Try to access the workflow list without API key
echo "📋 Checking existing workflows..."
WORKFLOWS=$(curl -s "http://localhost:5678/api/v1/workflows" 2>/dev/null || echo '{"data":[]}')

echo "Workflows response: $WORKFLOWS"

# Try to trigger the webhook directly (it should be auto-registered)
echo "🎯 Testing webhook trigger..."
WEBHOOK_RESPONSE=$(curl -s -X POST \
    -H "Content-Type: application/json" \
    -d '{
        "taskFile": "/private/var/www/2025/ollamar1/beauty-crm/shared-product-engineering/sdlc-monster-agents/tasks/planner-ui-fixes.md",
        "timestamp": "2025-08-02T10:35:00Z",
        "source": "direct-activation-test"
    }' \
    "http://localhost:5678/webhook/task-trigger" 2>/dev/null || echo "Webhook not found")

echo "Webhook response: $WEBHOOK_RESPONSE"

# If webhook not found, try to access n8n UI directly
if [[ "$WEBHOOK_RESPONSE" == *"not registered"* ]]; then
    echo "⚠️  Webhook not registered - workflow needs to be activated"
    echo "🌐 Please open http://localhost:5678 in your browser to activate the workflow"
    echo "📝 Look for 'Autonomous Task Executor - Production Ready' and toggle it ON"
else
    echo "✅ Webhook is working!"
    echo "🎉 Autonomous task executor is ready!"
fi
