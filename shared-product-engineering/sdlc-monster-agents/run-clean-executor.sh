#!/bin/bash

# Clean Autonomous Task Executor - Single streamlined implementation
set -e

echo "🚀 Clean Autonomous Task Executor"
echo "Single streamlined implementation - no redundant files!"
echo ""

# Configuration
TASK_FILE="/private/var/www/2025/ollamar1/beauty-crm/shared-product-engineering/sdlc-monster-agents/tasks/planner-ui-fixes.md"
LOG_FILE="/private/var/www/2025/ollamar1/beauty-crm/shared-product-engineering/sdlc-monster-agents/logs/execution.log"

# Ensure logs directory exists
mkdir -p "$(dirname "$LOG_FILE")"

# Log function
log_operation() {
    local message="$1"
    local timestamp=$(date -u +"%Y-%m-%dT%H:%M:%S.%3NZ")
    echo "[$timestamp] $message" | tee -a "$LOG_FILE"
}

# Process tasks function (same logic as n8n workflow)
process_tasks() {
    log_operation "Starting task processing..."
    
    if [ ! -f "$TASK_FILE" ]; then
        log_operation "ERROR: Task file not found: $TASK_FILE"
        return 1
    fi
    
    log_operation "Reading tasks from: $TASK_FILE"
    
    local task_count=0
    local solutions=()
    
    # Parse markdown tasks
    while IFS= read -r line; do
        if [[ "$line" =~ ^-\ \[\ \]\ (.+)$ ]]; then
            task_count=$((task_count + 1))
            local description="${BASH_REMATCH[1]}"
            
            log_operation "Task $task_count: $description"
            
            # Extract file path and code context (simplified)
            local file_path=""
            local code_context=""
            
            # Generate AI solution (mock for now)
            local solution="AI Solution for: $description"
            local confidence="0.9"
            
            solutions+=("{\"task\":\"$description\",\"solution\":\"$solution\",\"confidence\":$confidence,\"status\":\"generated\"}")
            
            log_operation "✅ Solution generated (confidence: $confidence)"
        fi
    done < "$TASK_FILE"
    
    log_operation "Processed $task_count tasks successfully!"
    
    # Create JSON response
    local json_response="{\"success\":true,\"tasksProcessed\":$task_count,\"tasks\":[$(IFS=,; echo "${solutions[*]}")],\"timestamp\":\"$(date -u +%Y-%m-%dT%H:%M:%S.%3NZ)\"}"
    
    echo ""
    echo "📊 EXECUTION RESULTS:"
    echo "$json_response" | jq '.' 2>/dev/null || echo "$json_response"
    
    return 0
}

# Test webhook endpoint
test_webhook() {
    log_operation "Testing webhook endpoint..."
    
    local response=$(curl -s -X POST \
        -H "Content-Type: application/json" \
        -d "{\"taskFile\":\"$TASK_FILE\"}" \
        "http://localhost:5678/webhook/execute-tasks" 2>/dev/null || echo "webhook_failed")
    
    if [[ "$response" == *"not registered"* ]] || [[ "$response" == "webhook_failed" ]]; then
        log_operation "⚠️  Webhook not available - using direct processing"
        return 1
    else
        log_operation "✅ Webhook response received"
        echo "$response" | jq '.' 2>/dev/null || echo "$response"
        return 0
    fi
}

# Main execution
echo "🎯 Starting execution..."
echo ""

# Try webhook first, fallback to direct processing
if ! test_webhook; then
    echo "🔄 Running direct task processing..."
    process_tasks
fi

echo ""
echo "🎉 Clean Autonomous Task Executor completed!"
echo ""
echo "📁 Files:"
echo "   - Tasks: $TASK_FILE"
echo "   - Logs: $LOG_FILE"
echo "   - Workflow: workflows/autonomous-task-executor-clean.json"
echo ""
echo "🔧 To activate n8n workflow:"
echo "   1. Open http://localhost:5678"
echo "   2. Import workflows/autonomous-task-executor-clean.json"
echo "   3. Activate the workflow"
echo "   4. Re-run this script"
