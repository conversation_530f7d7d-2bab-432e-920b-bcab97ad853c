#!/bin/bash

# Autonomous Task Executor - Production Ready Script
# Based on comprehensive research and implementation blueprint

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
WORKFLOW_FILE="$SCRIPT_DIR/workflows/autonomous-task-executor.json"
TASK_FILE="$SCRIPT_DIR/tasks/planner-ui-fixes.md"
LOG_DIR="$SCRIPT_DIR/logs"
BACKUP_DIR="$SCRIPT_DIR/backups"

# Ensure required directories exist
mkdir -p "$LOG_DIR" "$BACKUP_DIR"

echo -e "${BLUE}🚀 Starting Autonomous Task Executor${NC}"
echo -e "${BLUE}Based on comprehensive research and production-ready architecture${NC}"
echo ""

# Check prerequisites
echo -e "${YELLOW}📋 Checking prerequisites...${NC}"

# Check if n8n is running
if ! curl -s http://localhost:5678/api/v1/workflows > /dev/null 2>&1; then
    echo -e "${RED}❌ n8n is not running on localhost:5678${NC}"
    echo -e "${YELLOW}Starting n8n with Docker Compose...${NC}"
    
    if [ -f "$SCRIPT_DIR/docker-compose.yml" ]; then
        cd "$SCRIPT_DIR"
        docker compose up -d
        echo -e "${GREEN}✅ n8n started successfully${NC}"
        
        # Wait for n8n to be ready
        echo -e "${YELLOW}⏳ Waiting for n8n to be ready...${NC}"
        for i in {1..30}; do
            if curl -s http://localhost:5678/api/v1/workflows > /dev/null 2>&1; then
                echo -e "${GREEN}✅ n8n is ready${NC}"
                break
            fi
            sleep 2
            echo -n "."
        done
    else
        echo -e "${RED}❌ docker-compose.yml not found. Please start n8n manually.${NC}"
        exit 1
    fi
fi

# Check if Gemini API key is set
if [ -z "$GEMINI_API_KEY" ]; then
    echo -e "${RED}❌ GEMINI_API_KEY environment variable is not set${NC}"
    echo -e "${YELLOW}Please set your Gemini API key:${NC}"
    echo "export GEMINI_API_KEY='your_api_key_here'"
    exit 1
fi

# Check if workflow file exists
if [ ! -f "$WORKFLOW_FILE" ]; then
    echo -e "${RED}❌ Workflow file not found: $WORKFLOW_FILE${NC}"
    exit 1
fi

# Check if task file exists
if [ ! -f "$TASK_FILE" ]; then
    echo -e "${RED}❌ Task file not found: $TASK_FILE${NC}"
    exit 1
fi

echo -e "${GREEN}✅ All prerequisites met${NC}"
echo ""

# Import workflow to n8n
echo -e "${YELLOW}📥 Importing workflow to n8n...${NC}"

# First, check if workflow already exists
WORKFLOW_NAME="Autonomous Task Executor - Production Ready"
EXISTING_WORKFLOW=$(curl -s -H "Content-Type: application/json" \
    "http://localhost:5678/api/v1/workflows" | \
    jq -r ".data[] | select(.name == \"$WORKFLOW_NAME\") | .id" 2>/dev/null || echo "")

if [ -n "$EXISTING_WORKFLOW" ] && [ "$EXISTING_WORKFLOW" != "null" ]; then
    echo -e "${YELLOW}⚠️  Workflow already exists with ID: $EXISTING_WORKFLOW${NC}"
    echo -e "${YELLOW}Updating existing workflow...${NC}"
    
    # Update existing workflow
    WORKFLOW_RESPONSE=$(curl -s -X PUT \
        -H "Content-Type: application/json" \
        -d @"$WORKFLOW_FILE" \
        "http://localhost:5678/api/v1/workflows/$EXISTING_WORKFLOW")
    
    WORKFLOW_ID="$EXISTING_WORKFLOW"
else
    # Create new workflow
    WORKFLOW_RESPONSE=$(curl -s -X POST \
        -H "Content-Type: application/json" \
        -d @"$WORKFLOW_FILE" \
        "http://localhost:5678/api/v1/workflows")
    
    WORKFLOW_ID=$(echo "$WORKFLOW_RESPONSE" | jq -r '.data.id' 2>/dev/null || echo "")
fi

if [ -z "$WORKFLOW_ID" ] || [ "$WORKFLOW_ID" = "null" ]; then
    echo -e "${RED}❌ Failed to import/update workflow${NC}"
    echo "Response: $WORKFLOW_RESPONSE"
    exit 1
fi

echo -e "${GREEN}✅ Workflow imported/updated successfully with ID: $WORKFLOW_ID${NC}"

# Activate workflow
echo -e "${YELLOW}🔄 Activating workflow...${NC}"
ACTIVATE_RESPONSE=$(curl -s -X POST \
    "http://localhost:5678/api/v1/workflows/$WORKFLOW_ID/activate")

if echo "$ACTIVATE_RESPONSE" | jq -e '.data.active' > /dev/null 2>&1; then
    echo -e "${GREEN}✅ Workflow activated successfully${NC}"
else
    echo -e "${YELLOW}⚠️  Workflow activation response: $ACTIVATE_RESPONSE${NC}"
fi

# Get webhook URL
echo -e "${YELLOW}🔗 Getting webhook URL...${NC}"
WEBHOOK_URL="http://localhost:5678/webhook/task-trigger"

echo -e "${GREEN}✅ Webhook URL: $WEBHOOK_URL${NC}"
echo ""

# Trigger the workflow with task file
echo -e "${YELLOW}🎯 Triggering autonomous task execution...${NC}"
echo -e "${BLUE}Task file: $TASK_FILE${NC}"
echo ""

# Create payload
PAYLOAD=$(jq -n \
    --arg taskFile "$TASK_FILE" \
    --arg timestamp "$(date -u +%Y-%m-%dT%H:%M:%S.%3NZ)" \
    '{
        taskFile: $taskFile,
        timestamp: $timestamp,
        source: "autonomous-script",
        mode: "production"
    }')

# Execute webhook
EXECUTION_RESPONSE=$(curl -s -X POST \
    -H "Content-Type: application/json" \
    -d "$PAYLOAD" \
    "$WEBHOOK_URL")

echo -e "${GREEN}✅ Workflow triggered successfully${NC}"
echo -e "${BLUE}Response:${NC}"
echo "$EXECUTION_RESPONSE" | jq '.' 2>/dev/null || echo "$EXECUTION_RESPONSE"
echo ""

# Monitor execution
echo -e "${YELLOW}📊 Monitoring execution...${NC}"
echo -e "${BLUE}You can monitor the execution in n8n UI: http://localhost:5678${NC}"
echo -e "${BLUE}Logs are being written to: $LOG_DIR/task-execution.log${NC}"
echo ""

# Show recent log entries
if [ -f "$LOG_DIR/task-execution.log" ]; then
    echo -e "${YELLOW}📝 Recent log entries:${NC}"
    tail -n 10 "$LOG_DIR/task-execution.log" 2>/dev/null || echo "No logs yet..."
fi

echo ""
echo -e "${GREEN}🎉 Autonomous Task Executor is now running!${NC}"
echo ""
echo -e "${BLUE}Next steps:${NC}"
echo -e "1. Monitor progress in n8n UI: ${YELLOW}http://localhost:5678${NC}"
echo -e "2. Check logs: ${YELLOW}tail -f $LOG_DIR/task-execution.log${NC}"
echo -e "3. Review task updates: ${YELLOW}cat $TASK_FILE${NC}"
echo -e "4. Check backups: ${YELLOW}ls -la $BACKUP_DIR/${NC}"
echo ""
echo -e "${BLUE}The system will autonomously:${NC}"
echo -e "• Parse markdown tasks with code context"
echo -e "• Analyze files and generate AI solutions"
echo -e "• Execute safe modifications with backups"
echo -e "• Update task completion status"
echo -e "• Provide comprehensive logging"
echo ""
echo -e "${GREEN}Production-ready autonomous coding is now active! 🚀${NC}"
