import { IntroverticUIProvider } from '@beauty-crm/platform-introvertic-ui';
import { useEffect, useState } from 'react';
import AppointmentCalendar from './components/AppointmentCalendar';
import { AppointmentForm } from './components/AppointmentForm';
import SalonHeader from './components/SalonHeader';

// CSS is now imported in main.tsx for consistency with other services

// Mock salon data
const salonData = {
  description: 'Premium hair salon with exceptional service',
  id: 'salon1',
  name: 'Elegant Hair Salon',
};

export default function App() {
  const [selectedSlot, setSelectedSlot] = useState<{
    startTime: Date;
    endTime: Date;
  } | null>(null);
  const [refreshSlots, setRefreshSlots] = useState(0);
  const [appointmentSuccess, setAppointmentSuccess] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');

  // Add event listener for custom success event
  useEffect(() => {
    const handleAppointmentSuccess = (
      event: CustomEvent<{ message: string }>,
    ) => {
      setAppointmentSuccess(true);
      setSuccessMessage(event.detail.message);
      setSelectedSlot(null);
      setRefreshSlots((prev) => prev + 1);
    };

    window.addEventListener(
      'appointmentSuccess',
      handleAppointmentSuccess as EventListener,
    );
    return () => {
      window.removeEventListener(
        'appointmentSuccess',
        handleAppointmentSuccess as EventListener,
      );
    };
  }, []);

  return (
    <IntroverticUIProvider>
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
        {/* Main container with better spacing and backdrop */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="bg-white/70 backdrop-blur-sm rounded-3xl shadow-2xl border border-white/20 overflow-hidden">
            {/* Header section */}
            <div className="bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 p-8">
              <SalonHeader salon={salonData} />
            </div>

            {/* Success notification with enhanced styling */}
            {appointmentSuccess && (
              <div
                data-testid="success-message"
                className="fixed top-8 right-8 bg-gradient-to-r from-green-500 to-emerald-500 text-white px-6 py-4 rounded-2xl shadow-2xl border border-green-300/20 backdrop-blur-sm z-50 max-w-md"
              >
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0">
                    <svg
                      className="w-6 h-6 text-white"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <title>Success Icon</title>
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M5 13l4 4L19 7"
                      />
                    </svg>
                  </div>
                  <div>
                    <h3 className="font-bold text-lg mb-1">
                      Appointment Confirmed!
                    </h3>
                    <p className="text-green-50 text-sm leading-relaxed">
                      {successMessage}
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Main content area */}
            <div className="p-8 space-y-8">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* Calendar section */}
                <div className="space-y-6">
                  <div className="flex items-center space-x-3">
                    <div className="flex items-center justify-center w-10 h-10 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl shadow-lg">
                      <svg
                        className="w-5 h-5 text-white"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <title>Calendar Icon</title>
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                        />
                      </svg>
                    </div>
                    <h2 className="text-2xl font-bold bg-gradient-to-r from-gray-900 to-indigo-800 bg-clip-text text-transparent">
                      Select Appointment Time
                    </h2>
                  </div>
                  <div className="bg-white/50 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/30">
                    <AppointmentCalendar
                      salonId={salonData.id}
                      onSelectSlot={setSelectedSlot}
                      selectedSlot={selectedSlot}
                      refreshTrigger={refreshSlots}
                    />
                  </div>
                </div>

                {/* Form section */}
                <div className="space-y-6">
                  <div className="flex items-center space-x-3">
                    <div className="flex items-center justify-center w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl shadow-lg">
                      <svg
                        className="w-5 h-5 text-white"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <title>Form Icon</title>
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                        />
                      </svg>
                    </div>
                    <h2 className="text-2xl font-bold bg-gradient-to-r from-gray-900 to-purple-800 bg-clip-text text-transparent">
                      Complete Your Appointment
                    </h2>
                  </div>
                  <div className="bg-white/50 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/30">
                    {selectedSlot ? (
                      <AppointmentForm
                        salonId={salonData.id}
                        salonName={salonData.name}
                        startTime={selectedSlot.startTime}
                        onAppointmentComplete={() => {
                          setSelectedSlot(null);
                          setAppointmentSuccess(true);
                          setSuccessMessage(
                            'Your appointment has been successfully booked. A confirmation email has been sent to your email address.',
                          );
                          setRefreshSlots((prev) => prev + 1);
                        }}
                      />
                    ) : (
                      <div className="text-center py-12">
                        <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-gray-100 to-gray-200 rounded-2xl mb-4">
                          <svg
                            className="w-8 h-8 text-gray-400"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <title>Time Slot Icon</title>
                          </svg>
                        </div>
                        <h3 className="text-lg font-semibold text-gray-700 mb-2">
                          Select a Time Slot
                        </h3>
                        <p className="text-gray-500 max-w-sm mx-auto leading-relaxed">
                          Please select an appointment time from the calendar to
                          continue with your booking.
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </IntroverticUIProvider>
  );
}
