{"name": "Complete SDLC Monster Agents - Full Implementation", "active": true, "nodes": [{"parameters": {"httpMethod": "POST", "path": "sdlc-monster-complete", "responseMode": "responseNode", "options": {}}, "id": "webhook-trigger", "name": "Webhook Trigger", "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [240, 300], "webhookId": "sdlc-monster-complete"}, {"parameters": {"code": {"execute": {"code": "// File System Monitor - Comprehensive health checks and file analysis"}}}, "id": "file-monitor", "name": "File System Monitor", "type": "@n8n/n8n-nodes-langchain.code", "typeVersion": 1, "position": [460, 300]}, {"parameters": {"code": {"execute": {"code": "// Gemma AI Decision Engine - LangChain integration with gemma-3n-e4b-it"}}}, "id": "ai-decision-engine", "name": "Gemma AI Decision Engine", "type": "@n8n/n8n-nodes-langchain.code", "typeVersion": 1, "position": [680, 300]}, {"parameters": {"code": {"execute": {"code": "// Self-Healing File Operations - Atomic writes, backups, verification"}}}, "id": "self-healing-ops", "name": "Self-Healing File Operations", "type": "@n8n/n8n-nodes-langchain.code", "typeVersion": 1, "position": [900, 300]}, {"parameters": {"code": {"execute": {"code": "// Error Recovery & Monitoring - System health and comprehensive reporting"}}}, "id": "error-recovery", "name": "Error Recovery & Monitoring", "type": "@n8n/n8n-nodes-langchain.code", "typeVersion": 1, "position": [1120, 300]}, {"parameters": {"respondWith": "json", "responseBody": "={{ JSON.stringify($json.webhookResponse, null, 2) }}"}, "id": "webhook-response", "name": "Webhook Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1340, 300]}], "connections": {"Webhook Trigger": {"main": [[{"node": "File System Monitor", "type": "main", "index": 0}]]}, "File System Monitor": {"main": [[{"node": "Gemma AI Decision Engine", "type": "main", "index": 0}]]}, "Gemma AI Decision Engine": {"main": [[{"node": "Self-Healing File Operations", "type": "main", "index": 0}]]}, "Self-Healing File Operations": {"main": [[{"node": "Error Recovery & Monitoring", "type": "main", "index": 0}]]}, "Error Recovery & Monitoring": {"main": [[{"node": "Webhook Response", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1", "saveDataErrorExecution": "all", "saveDataSuccessExecution": "all", "saveManualExecutions": true, "saveExecutionProgress": true}}