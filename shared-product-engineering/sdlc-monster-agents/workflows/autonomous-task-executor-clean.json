{"name": "Autonomous Task Executor - Clean", "nodes": [{"parameters": {"httpMethod": "POST", "path": "execute-tasks", "responseMode": "responseNode", "options": {}}, "id": "webhook-trigger", "name": "Webhook Trigger", "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [240, 300], "webhookId": "autonomous-tasks"}, {"parameters": {"jsCode": "// Autonomous Task Processor\nconst fs = require('fs');\n\n// Get input data\nconst input = $input.all()[0].json;\nconst taskFile = input.body?.taskFile || '/private/var/www/2025/ollamar1/beauty-crm/shared-product-engineering/sdlc-monster-agents/tasks/planner-ui-fixes.md';\n\nconsole.log('Processing tasks from:', taskFile);\n\ntry {\n  // Read and parse tasks\n  const content = fs.readFileSync(taskFile, 'utf8');\n  const lines = content.split('\\n');\n  const tasks = [];\n  \n  for (let i = 0; i < lines.length; i++) {\n    const line = lines[i];\n    const taskMatch = line.match(/^- \\[ \\] (.+)$/);\n    \n    if (taskMatch) {\n      const description = taskMatch[1];\n      let codeContext = '';\n      let filePath = '';\n      \n      // Extract context from following lines\n      for (let j = i + 1; j < lines.length && j < i + 10; j++) {\n        if (lines[j].startsWith('Path:') || lines[j].startsWith('File:')) {\n          filePath = lines[j].replace(/^(Path|File):\\s*/, '').trim();\n        }\n        if (lines[j].startsWith('```')) {\n          j++; // Skip opening ```\n          while (j < lines.length && !lines[j].startsWith('```')) {\n            codeContext += lines[j] + '\\n';\n            j++;\n          }\n          break;\n        }\n        if (lines[j].startsWith('- [')) break;\n      }\n      \n      // Generate solution\n      const solution = {\n        task: description,\n        filePath,\n        codeContext: codeContext.trim(),\n        solution: `AI Solution for: ${description}`,\n        confidence: 0.9,\n        status: 'generated'\n      };\n      \n      tasks.push(solution);\n    }\n  }\n  \n  console.log(`Processed ${tasks.length} tasks`);\n  \n  return {\n    success: true,\n    tasksProcessed: tasks.length,\n    tasks: tasks,\n    timestamp: new Date().toISOString()\n  };\n  \n} catch (error) {\n  console.error('Error processing tasks:', error.message);\n  return {\n    success: false,\n    error: error.message,\n    timestamp: new Date().toISOString()\n  };\n}"}, "id": "task-processor", "name": "Task Processor", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [460, 300]}, {"parameters": {"respondWith": "json", "responseBody": "={{ JSON.stringify($json, null, 2) }}"}, "id": "webhook-response", "name": "Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [680, 300]}], "connections": {"Webhook Trigger": {"main": [[{"node": "Task Processor", "type": "main", "index": 0}]]}, "Task Processor": {"main": [[{"node": "Response", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1"}}