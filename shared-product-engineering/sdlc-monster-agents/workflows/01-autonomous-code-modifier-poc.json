{
  "name": "Autonomous Task Executor - Production Ready",
  "nodes": [
    {
      "parameters": {
        "httpMethod": "POST",
        "path": "task-trigger",
        "responseMode": "responseNode",
        "options": {}
      },
      "id": "webhook-trigger",
      "name": "Task Webhook Trigger",
      "type": "n8n-nodes-base.webhook",
      "typeVersion": 2,
      "position": [240, 300],
      "webhookId": "autonomous-task-webhook"
    },
    {
      "parameters": {
        "code": {
          "execute": {
            "code": "// Task Parser - Extract markdown tasks with context\nconst fs = require('fs');\nconst path = require('path');\n\n// Get webhook data\nconst webhookData = $input.all()[0].json;\nconst taskFile = webhookData.taskFile || '/private/var/www/2025/ollamar1/beauty-crm/shared-product-engineering/sdlc-monster-agents/tasks/planner-ui-fixes.md';\nconst logFile = '/private/var/www/2025/ollamar1/beauty-crm/shared-product-engineering/sdlc-monster-agents/logs/task-execution.log';\n\n// Ensure logs directory exists\nconst logDir = path.dirname(logFile);\nif (!fs.existsSync(logDir)) {\n  fs.mkdirSync(logDir, { recursive: true });\n}\n\n// Log function\nfunction logOperation(message) {\n  const timestamp = new Date().toISOString();\n  const logEntry = `[${timestamp}] ${message}\\n`;\n  fs.appendFileSync(logFile, logEntry);\n  console.log(logEntry.trim());\n}\n\ntry {\n  logOperation('Starting task parsing process');\n  \n  // Read task file\n  if (!fs.existsSync(taskFile)) {\n    throw new Error(`Task file does not exist: ${taskFile}`);\n  }\n  \n  const content = fs.readFileSync(taskFile, 'utf8');\n  logOperation(`Read task file: ${taskFile}`);\n  \n  // Parse markdown tasks\n  const tasks = [];\n  const lines = content.split('\\n');\n  \n  for (let i = 0; i < lines.length; i++) {\n    const line = lines[i];\n    const taskMatch = line.match(/^- \\[ \\] (.+)$/);\n    \n    if (taskMatch) {\n      const description = taskMatch[1];\n      let codeContext = '';\n      let filePath = '';\n      \n      // Extract code context and file path\n      for (let j = i + 1; j < lines.length; j++) {\n        if (lines[j].startsWith('```
')) {\n          // Found code block\n          const langMatch = lines[j].match(/
```(\\w+)?/);\n          const language = langMatch ? langMatch[1] : 'text';\n          j++; // Skip opening ```
\n          \n          while (j < lines.length && !lines[j].startsWith('
```')) {\n            codeContext += lines[j] + '\\n';\n            j++;\n          }\n          break;\n        }\n        if (lines[j].startsWith('File:') || lines[j].startsWith('Path:')) {\n          filePath = lines[j].replace(/^(File|Path):\\s*/, '').trim();\n        }\n        if (lines[j].startsWith('- [')) break; // Next task\n      }\n      \n      tasks.push({\n        id: Buffer.from(description).toString('base64').slice(0, 8),\n        description,\n        codeContext: codeContext.trim(),\n        filePath,\n        lineNumber: i + 1,\n        priority: description.includes('CRITICAL') ? 'high' : \n                 description.includes('urgent') ? 'medium' : 'normal'\n      });\n    }\n  }\n  \n  logOperation(`Parsed ${tasks.length} tasks from markdown file`);\n  \n  return tasks.map(task => ({ json: { ...task, taskFile, logFile, timestamp: new Date().toISOString() } }));\n  \n} catch (error) {\n  logOperation(`Task parsing ERROR: ${error.message}`);\n  return [{\n    json: {\n      error: error.message,\n      taskFile,\n      logFile,\n      timestamp: new Date().toISOString(),\n      operation: 'task-parsing-error'\n    }\n  }];\n}"
          }
        }
      },
      "id": "task-parser",
      "name": "Task Parser",
      "type": "@n8n/n8n-nodes-langchain.code",
      "typeVersion": 1,
      "position": [460, 300]
    },
    {
      "parameters": {
        "code": {
          "execute": {
            "code": "// File Context Analyzer - Read relevant files for task context\nconst fs = require('fs');\nconst path = require('path');\n\n// Get task data\nconst taskData = $input.all()[0].json;\n\nif (taskData.error) {\n  return [taskData]; // Pass through error\n}\n\nconst { description, filePath, codeContext, logFile } = taskData;\n\n// Log function\nfunction logOperation(message) {\n  const timestamp = new Date().toISOString();\n  const logEntry = `[${timestamp}] ${message}\\n`;\n  fs.appendFileSync(logFile, logEntry);\n  console.log(logEntry.trim());\n}\n\ntry {\n  logOperation(`Analyzing context for task: ${description}`);\n  \n  let fileContent = '';\n  let relatedFiles = [];\n  \n  // If specific file path provided, read it\n  if (filePath && fs.existsSync(filePath)) {\n    fileContent = fs.readFileSync(filePath, 'utf8');\n    logOperation(`Read target file: ${filePath}`);\n  } else {\n    // Auto-detect relevant files based on task description\n    const baseDir = '/private/var/www/2025/ollamar1/beauty-crm';\n    \n    if (description.toLowerCase().includes('planner') || description.toLowerCase().includes('appointment')) {\n      const plannerDir = path.join(baseDir, 'services/appointment/appointment-planner-frontend/src');\n      if (fs.existsSync(plannerDir)) {\n        // Find relevant React components\n        const findReactFiles = (dir) => {\n          const files = [];\n          try {\n            const items = fs.readdirSync(dir);\n            for (const item of items) {\n              const fullPath = path.join(dir, item);\n              const stat = fs.statSync(fullPath);\n              if (stat.isDirectory() && !item.startsWith('.')) {\n                files.push(...findReactFiles(fullPath));\n              } else if (item.endsWith('.tsx') || item.endsWith('.ts')) {\n                files.push(fullPath);\n              }\n            }\n          } catch (e) {\n            // Ignore permission errors\n          }\n          return files;\n        };\n        \n        relatedFiles = findReactFiles(plannerDir).slice(0, 5); // Limit to 5 files\n        logOperation(`Found ${relatedFiles.length} related files`);\n      }\n    }\n  }\n  \n  return [{\n    json: {\n      ...taskData,\n      fileContent,\n      relatedFiles,\n      contextAnalyzed: true,\n      operation: 'context-analysis-success'\n    }\n  }];\n  \n} catch (error) {\n  logOperation(`Context analysis ERROR: ${error.message}`);\n  return [{\n    json: {\n      ...taskData,\n      error: error.message,\n      operation: 'context-analysis-error'\n    }\n  }];\n}"
          }
        }
      },
      "id": "context-analyzer",
      "name": "File Context Analyzer",
      "type": "@n8n/n8n-nodes-langchain.code",
      "typeVersion": 1,
      "position": [680, 300]
    },
    {
      "parameters": {
        "modelName": "models/gemini-2.0-flash-exp"
      },
      "id": "gemini-model",
      "name": "Gemini AI Model",
      "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini",
      "typeVersion": 1,
      "position": [900, 300]
    },
    {
      "parameters": {
        "code": {
          "execute": {
            "code": "// AI Task Processor - Generate solutions using Gemini\nconst { ChatGoogleGenerativeAI } = require('@langchain/google-genai');\nconst fs = require('fs');\n\n// Get task data\nconst taskData = $input.all()[0].json;\n\nif (taskData.error) {\n  return [taskData]; // Pass through error\n}\n\nconst { description, fileContent, codeContext, relatedFiles, logFile } = taskData;\n\n// Log function\nfunction logOperation(message) {\n  const timestamp = new Date().toISOString();\n  const logEntry = `[${timestamp}] ${message}\\n`;\n  fs.appendFileSync(logFile, logEntry);\n  console.log(logEntry.trim());\n}\n\ntry {\n  // Initialize Gemini AI\n  const model = new ChatGoogleGenerativeAI({\n    modelName: 'gemini-2.0-flash-exp',\n    apiKey: process.env.GEMINI_API_KEY,\n    temperature: 0.1,\n    maxOutputTokens: 4096\n  });\n  \n  logOperation('Initialized Gemini AI for task processing');\n  \n  // Build comprehensive prompt\n  let prompt = `You are an expert software engineer working on the Beauty CRM system. Analyze this task and provide a complete solution.\n\nTask: ${description}\n\n`;\n  \n  if (fileContent) {\n    prompt += `Current file content:\n\\`\\`\\`\n${fileContent.slice(0, 3000)}\n\\`\\`\\`\n\n`;\n  }\n  \n  if (codeContext) {\n    prompt += `Code context:\n\\`\\`\\`\n${codeContext}\n\\`\\`\\`\n\n`;\n  }\n  \n  if (relatedFiles && relatedFiles.length > 0) {\n    prompt += `Related files to consider: ${relatedFiles.join(', ')}\\n\\n`;\n  }\n  \n  prompt += `Please provide a solution in this JSON format:\n{\n  \"analysis\": \"Brief analysis of the issue and approach\",\n  \"solution\": \"Complete code solution or fix\",\n  \"files\": [\"list of files to modify\"],\n  \"commands\": [\"any shell commands to run\"],\n  \"tests\": \"Test code to verify the solution\",\n  \"confidence\": 0.95,\n  \"reasoning\": \"Explanation of the solution approach\"\n}\n\nFocus on:\n1. Beauty CRM specific patterns and conventions\n2. React/TypeScript best practices\n3. Introvertic UI component usage\n4. Security and performance considerations\n5. Comprehensive error handling`;\n  \n  logOperation('Sending task to Gemini AI for solution generation');\n  \n  // Get AI response\n  const response = await model.invoke(prompt);\n  let aiResponse = response.content;\n  \n  // Clean up response if it contains markdown\n  if (aiResponse.includes('```json
')) {\n    aiResponse = aiResponse.replace(/
```json\\n?/, '').replace(/\\n?```$/, '');\n  }\n  \n  let solution;\n  try {\n    solution = JSON.parse(aiResponse);\n  } catch (parseError) {\n    // If JSON parsing fails, create a structured response\n    solution = {\n      analysis: \"AI response parsing failed\",\n      solution: aiResponse,\n      files: [],\n      commands: [],\n      tests: \"\",\n      confidence: 0.5,\n      reasoning: \"Raw AI response due to parsing error\"\n    };\n  }\n  \n  logOperation('Received solution from Gemini AI');\n  logOperation(`Solution confidence: ${solution.confidence}`);\n  \n  return [{\n    json: {\n      ...taskData,\n      aiSolution: solution,\n      operation: 'ai-processing-success',\n      aiModel: 'gemini-2.0-flash-exp'\n    }\n  }];\n  \n} catch (error) {\n  logOperation(`AI processing ERROR: ${error.message}`);\n  return [{\n    json: {\n      ...taskData,\n      error: error.message,\n      operation: 'ai-processing-error'\n    }\n  }];\n}"
          }
        }
      },
      "id": "ai-processor",
      "name": "AI Task Processor",
      "type": "@n8n/n8n-nodes-langchain.code",
      "typeVersion": 1,
      "position": [1120, 300]
    },
    {
      "parameters": {
        "code": {
          "execute": {
            "code": "// Solution Executor - Apply AI-generated solutions safely\nconst fs = require('fs');\nconst path = require('path');\nconst { execSync } = require('child_process');\n\n// Get task data with AI solution\nconst taskData = $input.all()[0].json;\n\nif (taskData.error) {\n  return [taskData]; // Pass through error\n}\n\nconst { aiSolution, filePath, logFile, description } = taskData;\n\n// Log function\nfunction logOperation(message) {\n  const timestamp = new Date().toISOString();\n  const logEntry = `[${timestamp}] ${message}\\n`;\n  fs.appendFileSync(logFile, logEntry);\n  console.log(logEntry.trim());\n}\n\ntry {\n  logOperation(`Executing solution for task: ${description}`);\n  \n  const executionResults = [];\n  \n  // Check confidence threshold\n  if (aiSolution.confidence < 0.7) {\n    logOperation(`WARNING: Low confidence solution (${aiSolution.confidence}), requiring manual review`);\n    return [{\n      json: {\n        ...taskData,\n        executionStatus: 'manual-review-required',\n        reason: 'Low confidence solution',\n        operation: 'execution-skipped'\n      }\n    }];\n  }\n  \n  // Create backups before making changes\n  const backupDir = '/private/var/www/2025/ollamar1/beauty-crm/shared-product-engineering/sdlc-monster-agents/backups';\n  if (!fs.existsSync(backupDir)) {\n    fs.mkdirSync(backupDir, { recursive: true });\n  }\n  \n  const timestamp = Date.now();\n  \n  // Process file modifications\n  if (aiSolution.files && aiSolution.files.length > 0) {\n    for (const file of aiSolution.files) {\n      try {\n        if (fs.existsSync(file)) {\n          // Create backup\n          const backupFile = path.join(backupDir, `${path.basename(file)}.${timestamp}.backup`);\n          fs.copyFileSync(file, backupFile);\n          logOperation(`Created backup: ${backupFile}`);\n          \n          // Apply solution (for now, just log what would be done)\n          logOperation(`Would modify file: ${file}`);\n          executionResults.push({\n            file,\n            action: 'backup-created',\n            backup: backupFile,\n            status: 'ready-for-modification'\n          });\n        } else {\n          logOperation(`File not found: ${file}`);\n          executionResults.push({\n            file,\n            action: 'file-not-found',\n            status: 'error'\n          });\n        }\n      } catch (fileError) {\n        logOperation(`Error processing file ${file}: ${fileError.message}`);\n        executionResults.push({\n          file,\n          action: 'error',\n          error: fileError.message,\n          status: 'failed'\n        });\n      }\n    }\n  }\n  \n  // Execute safe commands (limited set for security)\n  if (aiSolution.commands && aiSolution.commands.length > 0) {\n    const safeCommands = ['npm install', 'npm test', 'npm run build', 'git status', 'git diff'];\n    \n    for (const command of aiSolution.commands) {\n      const isSafe = safeCommands.some(safe => command.startsWith(safe));\n      \n      if (isSafe) {\n        try {\n          logOperation(`Executing safe command: ${command}`);\n          // For now, just log - actual execution would be here\n          executionResults.push({\n            command,\n            action: 'command-logged',\n            status: 'ready-for-execution'\n          });\n        } catch (cmdError) {\n          logOperation(`Command execution error: ${cmdError.message}`);\n          executionResults.push({\n            command,\n            action: 'command-error',\n            error: cmdError.message,\n            status: 'failed'\n          });\n        }\n      } else {\n        logOperation(`Unsafe command blocked: ${command}`);\n        executionResults.push({\n          command,\n          action: 'blocked-unsafe',\n          status: 'security-blocked'\n        });\n      }\n    }\n  }\n  \n  logOperation('Solution execution completed');\n  \n  return [{\n    json: {\n      ...taskData,\n      executionResults,\n      executionStatus: 'completed',\n      backupTimestamp: timestamp,\n      operation: 'execution-success'\n    }\n  }];\n  \n} catch (error) {\n  logOperation(`Execution ERROR: ${error.message}`);\n  return [{\n    json: {\n      ...taskData,\n      error: error.message,\n      executionStatus: 'failed',\n      operation: 'execution-error'\n    }\n  }];\n}"
          }
        }
      },
      "id": "solution-executor",
      "name": "Solution Executor",
      "type": "@n8n/n8n-nodes-langchain.code",
      "typeVersion": 1,
      "position": [1340, 300]
    },
    {
      "parameters": {
        "code": {
          "execute": {
            "code": "// Task Completion Updater - Update markdown with task status\nconst fs = require('fs');\n\n// Get execution results\nconst taskData = $input.all()[0].json;\n\nif (taskData.error && !taskData.executionResults) {\n  return [taskData]; // Pass through error if no execution attempted\n}\n\nconst { taskFile, description, lineNumber, executionStatus, logFile } = taskData;\n\n// Log function\nfunction logOperation(message) {\n  const timestamp = new Date().toISOString();\n  const logEntry = `[${timestamp}] ${message}\\n`;\n  fs.appendFileSync(logFile, logEntry);\n  console.log(logEntry.trim());\n}\n\ntry {\n  logOperation(`Updating task completion status for: ${description}`);\n  \n  if (!fs.existsSync(taskFile)) {\n    throw new Error(`Task file not found: ${taskFile}`);\n  }\n  \n  // Read current task file\n  const content = fs.readFileSync(taskFile, 'utf8');\n  const lines = content.split('\\n');\n  \n  // Find and update the specific task\n  let updated = false;\n  for (let i = 0; i < lines.length; i++) {\n    const line = lines[i];\n    if (line.includes(description) && line.startsWith('- [ ]')) {\n      if (executionStatus === 'completed') {\n        lines[i] = line.replace('- [ ]', '- [x]');\n        logOperation(`Marked task as completed: ${description}`);\n      } else if (executionStatus === 'manual-review-required') {\n        lines[i] = line.replace('- [ ]', '- [?]') + ' (Manual review required)';\n        logOperation(`Marked task for manual review: ${description}`);\n      } else if (executionStatus === 'failed') {\n        lines[i] = line.replace('- [ ]', '- [!]') + ' (Execution failed)';\n        logOperation(`Marked task as failed: ${description}`);\n      }\n      updated = true;\n      break;\n    }\n  }\n  \n  if (updated) {\n    // Write updated content back to file\n    fs.writeFileSync(taskFile, lines.join('\\n'), 'utf8');\n    logOperation('Task file updated successfully');\n  } else {\n    logOperation('Task not found in file for update');\n  }\n  \n  return [{\n    json: {\n      ...taskData,\n      taskUpdated: updated,\n      finalStatus: executionStatus,\n      operation: 'task-update-success'\n    }\n  }];\n  \n} catch (error) {\n  logOperation(`Task update ERROR: ${error.message}`);\n  return [{\n    json: {\n      ...taskData,\n      error: error.message,\n      operation: 'task-update-error'\n    }\n  }];\n}"
          }
        }
      },
      "id": "task-updater",
      "name": "Task Completion Updater",
      "type": "@n8n/n8n-nodes-langchain.code",
      "typeVersion": 1,
      "position": [1560, 300]
    },
    {
      "parameters": {
        "respondWith": "json",
        "responseBody": "={{ JSON.stringify({\n  taskId: $json.id,\n  description: $json.description,\n  status: $json.finalStatus || $json.executionStatus,\n  aiSolution: $json.aiSolution,\n  executionResults: $json.executionResults,\n  timestamp: $json.timestamp,\n  success: !$json.error\n}, null, 2) }}"
      },
      "id": "webhook-response",
      "name": "Webhook Response",
      "type": "n8n-nodes-base.respondToWebhook",
      "typeVersion": 1,
      "position": [1780, 300]
    }
  ],
  "connections": {
    "Task Webhook Trigger": {
      "main": [
        [
          {
            "node": "Task Parser",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Task Parser": {
      "main": [
        [
          {
            "node": "File Context Analyzer",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "File Context Analyzer": {
      "main": [
        [
          {
            "node": "AI Task Processor",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "AI Task Processor": {
      "main": [
        [
          {
            "node": "Solution Executor",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Solution Executor": {
      "main": [
        [
          {
            "node": "Task Completion Updater",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Task Completion Updater": {
      "main": [
        [
          {
            "node": "Webhook Response",
            "type": "main",
            "index": 0
          }
        ]
      ]
    }
  }
}
         "type": "main",
            "index": 0
          }
        ]
      ]
    }
  }
}

      ]
    },
    "File Reader": {
      "main": [
        [
          {
            "node": "AI Code Processor",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "AI Code Processor": {
      "main": [
        [
          {
            "node": "File Writer",
            "type": "main",
            "index": 0
          }
        ]
      ]
    }
  },
  "active": false,
  "settings": {},
  "createdAt": "2025-01-02T00:00:00.000Z",
  "updatedAt": "2025-01-02T00:00:00.000Z",
  "id": "autonomous-code-modifier-poc"
}
