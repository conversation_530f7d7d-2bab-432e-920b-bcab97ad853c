#!/usr/bin/env node

// Workflow Validation Script for SDLC Monster Agents POC
// This script validates the n8n workflow JSON structure

const fs = require('fs');
const path = require('path');

const WORKFLOW_FILE = path.join(
  __dirname,
  'workflows',
  '01-autonomous-code-modifier-poc.json',
);

console.log('🔍 SDLC Monster Agents - Workflow Validation');
console.log('===========================================');

function validateWorkflow() {
  try {
    // Check if workflow file exists
    if (!fs.existsSync(WORKFLOW_FILE)) {
      throw new Error(`Workflow file not found: ${WORKFLOW_FILE}`);
    }

    console.log('✅ Workflow file found');

    // Parse JSON
    const workflowContent = fs.readFileSync(WORKFLOW_FILE, 'utf8');
    const workflow = JSON.parse(workflowContent);

    console.log('✅ JSON is valid');

    // Validate required fields
    const requiredFields = ['name', 'nodes', 'connections'];
    for (const field of requiredFields) {
      if (!workflow[field]) {
        throw new Error(`Missing required field: ${field}`);
      }
    }

    console.log('✅ Required fields present');

    // Validate nodes
    if (!Array.isArray(workflow.nodes) || workflow.nodes.length === 0) {
      throw new Error('Nodes must be a non-empty array');
    }

    console.log(`✅ Found ${workflow.nodes.length} nodes`);

    // Validate each node
    const nodeTypes = {
      'n8n-nodes-base.manualTrigger': 'Manual Trigger',
      '@n8n/n8n-nodes-langchain.code': 'LangChain Code',
      '@n8n/n8n-nodes-langchain.lmChatGoogleGemini': 'Google Gemini Chat Model',
    };

    const foundNodes = {};

    for (const node of workflow.nodes) {
      if (!node.id || !node.name || !node.type) {
        throw new Error(
          `Invalid node structure: ${JSON.stringify(node, null, 2)}`,
        );
      }

      foundNodes[node.type] = (foundNodes[node.type] || 0) + 1;

      console.log(`  - ${node.name} (${node.type})`);
    }

    console.log('✅ All nodes have valid structure');

    // Validate connections
    if (typeof workflow.connections !== 'object') {
      throw new Error('Connections must be an object');
    }

    const connectionCount = Object.keys(workflow.connections).length;
    console.log(`✅ Found ${connectionCount} connection groups`);

    // Validate node types
    const expectedNodeTypes = [
      'n8n-nodes-base.manualTrigger',
      '@n8n/n8n-nodes-langchain.code',
      '@n8n/n8n-nodes-langchain.lmChatGoogleGemini',
    ];

    for (const expectedType of expectedNodeTypes) {
      if (!foundNodes[expectedType]) {
        console.log(`⚠️  Missing expected node type: ${expectedType}`);
      } else {
        console.log(
          `✅ Found ${foundNodes[expectedType]} node(s) of type: ${expectedType}`,
        );
      }
    }

    // Check for LangChain Code nodes (should have 3)
    const langchainCodeNodes = foundNodes['@n8n/n8n-nodes-langchain.code'] || 0;
    if (langchainCodeNodes !== 3) {
      console.log(
        `⚠️  Expected 3 LangChain Code nodes, found ${langchainCodeNodes}`,
      );
    } else {
      console.log('✅ Correct number of LangChain Code nodes');
    }

    // Validate workflow flow
    const nodeNames = workflow.nodes.map((n) => n.name);
    const expectedFlow = [
      'Manual Trigger',
      'File Reader',
      'AI Code Processor',
      'File Writer',
    ];

    console.log('\n🔗 Workflow Flow Validation:');
    for (const expectedNode of expectedFlow) {
      if (nodeNames.includes(expectedNode)) {
        console.log(`  ✅ ${expectedNode}`);
      } else {
        console.log(`  ❌ Missing: ${expectedNode}`);
      }
    }

    console.log('\n📊 Workflow Summary:');
    console.log(`  Name: ${workflow.name}`);
    console.log(`  Nodes: ${workflow.nodes.length}`);
    console.log(`  Connections: ${connectionCount}`);
    console.log(`  Active: ${workflow.active || false}`);

    console.log('\n🎯 Validation Result: SUCCESS');
    console.log('The workflow is ready for import into n8n!');

    return true;
  } catch (error) {
    console.error('\n❌ Validation Failed:');
    console.error(`   ${error.message}`);
    return false;
  }
}

// Run validation
const isValid = validateWorkflow();
process.exit(isValid ? 0 : 1);
