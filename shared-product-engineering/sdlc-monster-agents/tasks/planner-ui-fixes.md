# Beauty CRM Planner UI Fixes - Autonomous Task List

## Critical Issues (High Priority)

- [ ] Fix Router configuration error - Cannot render Router inside another Router
Path: services/appointment/appointment-planner-frontend/src/App.tsx
```typescript
// Current issue: Nested Router components causing render errors
// Need to use useRoutes hook instead of Routes/Route components
import { useRoutes } from 'react-router-dom';
```

- [ ] Create missing calendar.css file causing style loading errors
Path: services/appointment/appointment-planner-frontend/src/styles/calendar.css
```css
/* Missing CSS file for FullCalendar styling */
.fc-event {
  border-radius: 8px;
  border: none;
  padding: 4px 8px;
}
```

- [ ] Fix duplicate key error in AppointmentCalendar.tsx (duplicate 'start' property)
Path: services/appointment/appointment-planner-frontend/src/components/AppointmentCalendar.tsx
```typescript
// Remove duplicate 'start' property in object literal
// Check line around 262-285 for duplicate properties
```

- [ ] Fix CalendarEvent interface type mismatch with FullCalendar API
Path: services/appointment/appointment-planner-frontend/src/components/AppointmentCalendar.tsx
```typescript
// Update CalendarEvent interface to use EventInput from FullCalendar
import { EventInput } from '@fullcalendar/core';
interface CalendarEvent extends EventInput {
  // Updated interface definition
}
```

## UI Enhancement Issues (Medium Priority)

- [ ] Add clear visual distinction between available and booked time slots in week view
Path: services/appointment/appointment-planner-frontend/src/components/AppointmentCalendar.tsx
```css
.available-slot {
  background-color: #e8f5e8;
  border: 2px dashed #4caf50;
}
.booked-slot {
  background-color: #ffebee;
  border: 2px solid #f44336;
}
```

- [ ] Fix appointment durations not being visually proportional to time length in day view
Path: services/appointment/appointment-planner-frontend/src/components/AppointmentCalendar.tsx
```typescript
// Calculate proper height based on appointment duration
const calculateEventHeight = (duration: number) => {
  return (duration / 60) * 60; // 60px per hour
};
```

- [ ] Improve month view to show appointment details for dense appointment schedules
Path: services/appointment/appointment-planner-frontend/src/components/AppointmentCalendar.tsx
```typescript
// Add tooltip or popup for appointment details in month view
const renderMonthEvent = (eventInfo: any) => {
  return (
    <Tooltip content={`${eventInfo.event.title} - ${eventInfo.event.extendedProps.clientName}`}>
      <div className="month-event-compact">
        {eventInfo.event.title}
      </div>
    </Tooltip>
  );
};
```

- [ ] Add quick-add capability for walk-in clients in client selection
Path: services/appointment/appointment-planner-frontend/src/components/AppointmentForm.tsx
```typescript
// Add quick client creation form
const QuickAddClient = () => {
  return (
    <div className="quick-add-client">
      <input placeholder="Client Name" />
      <input placeholder="Phone" />
      <Button onClick={handleQuickAdd}>Add Client</Button>
    </div>
  );
};
```

- [ ] Implement service bundling - multiple services in one appointment
Path: services/appointment/appointment-planner-frontend/src/components/AppointmentForm.tsx
```typescript
// Multi-select service component
const ServiceBundleSelector = () => {
  const [selectedServices, setSelectedServices] = useState([]);
  return (
    <MultiSelect
      options={services}
      value={selectedServices}
      onChange={setSelectedServices}
      placeholder="Select multiple services"
    />
  );
};
```

## Mobile Responsiveness Issues (Medium Priority)

- [ ] Fix UI elements overlapping on small screens (< 768px)
Path: services/appointment/appointment-planner-frontend/src/App.tsx
```css
@media (max-width: 768px) {
  .grid-cols-1.lg\\:grid-cols-2 {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  .calendar-container {
    overflow-x: auto;
  }
}
```

- [ ] Improve modal z-index to prevent displaying behind calendar elements
Path: services/appointment/appointment-planner-frontend/src/components/AppointmentForm.tsx
```css
.appointment-modal {
  z-index: 9999;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}
```

## Code Quality Issues (Low Priority)

- [ ] Add comprehensive error handling for API calls
Path: services/appointment/appointment-planner-frontend/src/components/AppointmentCalendar.tsx
```typescript
// Wrap API calls in try-catch with user-friendly error messages
const fetchAppointments = async () => {
  try {
    const response = await api.getAppointments();
    return response.data;
  } catch (error) {
    console.error('Failed to fetch appointments:', error);
    showErrorNotification('Unable to load appointments. Please try again.');
    return [];
  }
};
```

- [ ] Implement consistent appointment color-coding for status identification
Path: services/appointment/appointment-planner-frontend/src/components/AppointmentCalendar.tsx
```typescript
const getStatusColor = (status: string) => {
  const colors = {
    'confirmed': '#4caf50',
    'pending': '#ff9800',
    'cancelled': '#f44336',
    'completed': '#2196f3'
  };
  return colors[status] || '#9e9e9e';
};
```

- [ ] Add loading states for better user experience
Path: services/appointment/appointment-planner-frontend/src/components/AppointmentCalendar.tsx
```typescript
const [isLoading, setIsLoading] = useState(false);

// Show loading spinner during data fetch
{isLoading && <LoadingSpinner />}
```

- [ ] Add unit tests for appointment calendar components
Path: services/appointment/appointment-planner-frontend/src/components/__tests__/AppointmentCalendar.test.tsx
```typescript
import { render, screen } from '@testing-library/react';
import AppointmentCalendar from '../AppointmentCalendar';

describe('AppointmentCalendar', () => {
  it('renders calendar without crashing', () => {
    render(<AppointmentCalendar salonId="test" />);
    expect(screen.getByRole('grid')).toBeInTheDocument();
  });
});
```

## Accessibility Improvements (Low Priority)

- [ ] Add proper ARIA labels for calendar navigation
Path: services/appointment/appointment-planner-frontend/src/components/AppointmentCalendar.tsx
```typescript
<button 
  aria-label="Previous month"
  onClick={handlePrevMonth}
>
  <ChevronLeft />
</button>
```

- [ ] Implement keyboard navigation for appointment slots
Path: services/appointment/appointment-planner-frontend/src/components/AppointmentCalendar.tsx
```typescript
const handleKeyDown = (event: KeyboardEvent) => {
  if (event.key === 'Enter' || event.key === ' ') {
    handleSlotSelect(slot);
  }
};
```

## Performance Optimizations (Low Priority)

- [ ] Implement virtual scrolling for large appointment lists
Path: services/appointment/appointment-planner-frontend/src/components/AppointmentList.tsx
```typescript
import { FixedSizeList as List } from 'react-window';

const VirtualizedAppointmentList = ({ appointments }) => (
  <List
    height={400}
    itemCount={appointments.length}
    itemSize={80}
  >
    {AppointmentItem}
  </List>
);
```

- [ ] Add memoization for expensive calendar calculations
Path: services/appointment/appointment-planner-frontend/src/components/AppointmentCalendar.tsx
```typescript
const memoizedCalendarEvents = useMemo(() => {
  return appointments.map(transformToCalendarEvent);
}, [appointments]);
```
