#!/bin/bash

# SDLC Monster Agents - Proof of Concept Startup Script
# This script sets up and runs the autonomous code modification POC

set -e

echo "🤖 SDLC Monster Agents - Autonomous Code Modification POC"
echo "========================================================="

# Configuration
PROJECT_ROOT="/private/var/www/2025/ollamar1/beauty-crm"
AGENTS_DIR="$PROJECT_ROOT/shared-product-engineering/sdlc-monster-agents"
WORKFLOW_FILE="$AGENTS_DIR/workflows/01-autonomous-code-modifier-poc.json"

# Check if we're in the right directory
if [ ! -d "$AGENTS_DIR" ]; then
    echo "❌ Error: SDLC Monster Agents directory not found at $AGENTS_DIR"
    exit 1
fi

cd "$AGENTS_DIR"

# Check for required environment variables
echo "🔍 Checking environment variables..."

if [ -z "$GEMINI_API_KEY" ]; then
    echo "❌ Error: GEMINI_API_KEY environment variable is required"
    echo "   Please set your Gemini API key:"
    echo "   export GEMINI_API_KEY='your_gemini_api_key_here'"
    exit 1
fi

echo "✅ GEMINI_API_KEY is set"

if [ -n "$LANGSMITH_API_KEY" ]; then
    echo "✅ LANGSMITH_API_KEY is set (optional)"
else
    echo "⚠️  LANGSMITH_API_KEY not set (optional for tracing)"
fi

# Create necessary directories
echo "📁 Creating directories..."
mkdir -p logs data workflows

# Check if test file exists
TEST_FILE="$AGENTS_DIR/test-files/sample-code.js"
if [ ! -f "$TEST_FILE" ]; then
    echo "❌ Error: Test file not found at $TEST_FILE"
    echo "   Please ensure the test file exists before running the POC"
    exit 1
fi

echo "✅ Test file found: $TEST_FILE"

# Check if workflow file exists
if [ ! -f "$WORKFLOW_FILE" ]; then
    echo "❌ Error: Workflow file not found at $WORKFLOW_FILE"
    exit 1
fi

echo "✅ Workflow file found: $WORKFLOW_FILE"

# Start n8n with Docker Compose
echo "🐳 Starting n8n with Docker Compose..."
docker compose up -d

# Wait for n8n to be ready
echo "⏳ Waiting for n8n to be ready..."
sleep 10

# Check if n8n is running
if ! curl -f http://localhost:5678/healthz > /dev/null 2>&1; then
    echo "❌ Error: n8n is not responding on http://localhost:5678"
    echo "   Check Docker logs: docker-compose logs n8n"
    exit 1
fi

echo "✅ n8n is running on http://localhost:5678"

# Display next steps
echo ""
echo "🎯 POC Setup Complete!"
echo "======================"
echo ""
echo "Next steps:"
echo "1. Open n8n in your browser: http://localhost:5678"
echo "2. Import the workflow: $WORKFLOW_FILE"
echo "3. Execute the workflow manually to test autonomous code modification"
echo ""
echo "The workflow will:"
echo "- Read the test file: $TEST_FILE"
echo "- Use Gemini AI to improve the code"
echo "- Write the improved code back to the filesystem"
echo "- Log all operations to: $AGENTS_DIR/logs/modification-log.txt"
echo ""
echo "To stop the POC:"
echo "  docker-compose down"
echo ""
echo "To view logs:"
echo "  docker-compose logs -f n8n"
echo "  tail -f logs/modification-log.txt"
echo ""
echo "🚀 Ready to demonstrate autonomous code modification!"
