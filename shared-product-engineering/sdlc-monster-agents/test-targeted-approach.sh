#!/bin/bash

# Test Targeted Approach - Demonstrate surgical fixes without overwrites
set -e

echo "🧪 Testing Targeted Approach - Surgical Code Fixes"
echo "Demonstrating how the agent makes small, targeted changes without overwrites"
echo ""

# Configuration
PLANNER_DIR="../../services/appointment/appointment-planner-frontend"
BACKUP_DIR="backups"
LOG_FILE="logs/targeted-test.log"

# Ensure directories exist
mkdir -p "$BACKUP_DIR" "$(dirname "$LOG_FILE")"

# Log function
log_operation() {
    local message="$1"
    local timestamp=$(date -u +"%Y-%m-%dT%H:%M:%S.%3NZ")
    echo "[$timestamp] $message" | tee -a "$LOG_FILE"
}

# Create backup function
create_backup() {
    local file_path="$1"
    if [ -f "$file_path" ]; then
        local backup_name="$(basename "$file_path").$(date +%s).backup"
        cp "$file_path" "$BACKUP_DIR/$backup_name"
        log_operation "✅ Backup created: $backup_name"
        echo "$BACKUP_DIR/$backup_name"
    fi
}

# Test 1: Targeted Router Fix (Small Context)
test_router_fix() {
    log_operation "🎯 Test 1: Targeted Router Fix"
    
    local app_file="$PLANNER_DIR/src/App.tsx"
    
    if [ ! -f "$app_file" ]; then
        log_operation "⚠️  Creating mock App.tsx for demonstration"
        mkdir -p "$(dirname "$app_file")"
        cat > "$app_file" << 'EOF'
import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { AppointmentCalendar } from './components/AppointmentCalendar';

function App() {
  return (
    <div className="App">
      <Router>
        <div className="container">
          <h1>Beauty CRM Planner</h1>
          <Router> {/* PROBLEM: Nested Router */}
            <Routes>
              <Route path="/" element={<AppointmentCalendar />} />
            </Routes>
          </Router>
        </div>
      </Router>
    </div>
  );
}

export default App;
EOF
    fi
    
    # Show original problematic code
    log_operation "📋 Original code (with nested Router problem):"
    grep -n "Router" "$app_file" | head -5
    
    # Create backup
    local backup_path=$(create_backup "$app_file")
    
    # Apply TARGETED fix (only fix the nested Router issue)
    log_operation "🔧 Applying targeted Router fix..."
    
    # Use sed to make surgical change - replace nested Router with div
    sed -i.tmp 's/<Router> {\/\* PROBLEM: Nested Router \*\/}/<div> {\/\* FIXED: Removed nested Router \*\/}/g' "$app_file"
    sed -i.tmp 's/<\/Router>/<\/div>/g' "$app_file"
    rm "$app_file.tmp"
    
    # Show fixed code
    log_operation "✅ Fixed code (surgical change only):"
    grep -n -A2 -B2 "FIXED" "$app_file"
    
    # Count lines changed
    local lines_changed=$(diff "$backup_path" "$app_file" | grep -c "^[<>]" || echo "0")
    log_operation "📊 Lines changed: $lines_changed (targeted approach)"
    
    echo "✅ Router fix applied successfully with minimal changes"
}

# Test 2: Targeted CSS Addition (No Overwrite)
test_css_addition() {
    log_operation "🎯 Test 2: Targeted CSS Addition"
    
    local css_file="$PLANNER_DIR/src/styles/calendar.css"
    mkdir -p "$(dirname "$css_file")"
    
    # Create existing CSS file
    if [ ! -f "$css_file" ]; then
        cat > "$css_file" << 'EOF'
/* Existing Calendar Styles */
.fc {
  font-family: Arial, sans-serif;
}

.fc-event {
  border-radius: 4px;
}
EOF
    fi
    
    log_operation "📋 Original CSS file size: $(wc -l < "$css_file") lines"
    
    # Create backup
    local backup_path=$(create_backup "$css_file")
    
    # Apply TARGETED addition (only add missing styles)
    log_operation "🔧 Adding targeted CSS rules..."
    
    cat >> "$css_file" << 'EOF'

/* TARGETED ADDITION: Status-based colors */
.fc-event.status-confirmed {
  background-color: #10b981;
}

.fc-event.status-pending {
  background-color: #f59e0b;
}
EOF
    
    log_operation "✅ New CSS file size: $(wc -l < "$css_file") lines"
    
    # Show what was added
    log_operation "📊 Added content:"
    tail -8 "$css_file"
    
    echo "✅ CSS addition applied successfully without overwriting existing styles"
}

# Test 3: Targeted TypeScript Interface Fix
test_interface_fix() {
    log_operation "🎯 Test 3: Targeted TypeScript Interface Fix"
    
    local types_file="$PLANNER_DIR/src/types/calendar.ts"
    mkdir -p "$(dirname "$types_file")"
    
    # Create existing interface with problem
    if [ ! -f "$types_file" ]; then
        cat > "$types_file" << 'EOF'
// Existing Calendar Types
export interface CalendarEvent {
  id: string;
  title: string;
  start: Date;
  start: string; // PROBLEM: Duplicate property
  allDay?: boolean;
}

export interface Appointment {
  id: string;
  clientId: string;
  startTime: Date;
}
EOF
    fi
    
    log_operation "📋 Original interface (with duplicate property):"
    grep -n "start:" "$types_file"
    
    # Create backup
    local backup_path=$(create_backup "$types_file")
    
    # Apply TARGETED fix (only fix the duplicate property)
    log_operation "🔧 Applying targeted interface fix..."
    
    # Remove the duplicate line
    sed -i.tmp '/start: string; \/\/ PROBLEM: Duplicate property/d' "$types_file"
    rm "$types_file.tmp"
    
    log_operation "✅ Fixed interface:"
    grep -n "start:" "$types_file"
    
    echo "✅ Interface fix applied successfully with surgical precision"
}

# Main execution
main() {
    log_operation "🚀 Starting targeted approach testing..."
    
    echo "🧪 This test demonstrates how the agent makes surgical fixes"
    echo "   instead of overwriting entire files like before."
    echo ""
    
    # Run targeted tests
    test_router_fix
    echo ""
    test_css_addition
    echo ""
    test_interface_fix
    
    echo ""
    echo "🎉 Targeted Approach Test Results:"
    echo ""
    echo "✅ **Router Fix**: Surgical change to remove nested Router"
    echo "✅ **CSS Addition**: Added styles without overwriting existing"
    echo "✅ **Interface Fix**: Removed duplicate property only"
    echo ""
    echo "📊 **Key Metrics**:"
    echo "   - All existing logic preserved"
    echo "   - Minimal lines changed per fix"
    echo "   - Automatic backups created"
    echo "   - Targeted, specific changes only"
    echo ""
    echo "🎯 **Comparison**:"
    echo "   ❌ Before: Entire files overwritten, logic lost"
    echo "   ✅ After: Surgical changes, logic preserved"
    echo ""
    echo "💾 Backups created in: $BACKUP_DIR"
    echo "📝 Detailed logs: $LOG_FILE"
}

# Execute main function
main
