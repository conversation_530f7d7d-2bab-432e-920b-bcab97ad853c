const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const redis = require('redis');
const axios = require('axios');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const path = require('path');
require('dotenv').config();

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: '*',
    methods: ['GET', 'POST'],
  },
});

// Redis client for caching and pub/sub
const redisClient = redis.createClient({
  url: process.env.REDIS_URL || 'redis://localhost:6379',
});

// Middleware
app.use(helmet());
app.use(cors());
app.use(morgan('combined'));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));
app.use(express.static(path.join(__dirname, 'public')));

// Store for pending approvals and agent activities
const pendingApprovals = new Map();
const agentActivities = new Map();
const agentPerformance = new Map();
const agentGrades = new Map();
const confidenceThreshold =
  Number.parseFloat(process.env.CONFIDENCE_THRESHOLD) || 0.1;

// Agent grading system - internship style
const GRADE_LEVELS = {
  F: {
    name: 'Failing',
    color: '#dc3545',
    minScore: 0,
    description: 'Needs immediate improvement',
  },
  D: {
    name: 'Poor',
    color: '#fd7e14',
    minScore: 40,
    description: 'Below expectations',
  },
  C: {
    name: 'Satisfactory',
    color: '#ffc107',
    minScore: 60,
    description: 'Meets basic requirements',
  },
  B: {
    name: 'Good',
    color: '#20c997',
    minScore: 75,
    description: 'Above average performance',
  },
  A: {
    name: 'Excellent',
    color: '#28a745',
    minScore: 90,
    description: 'Outstanding work',
  },
};

const PERFORMANCE_METRICS = {
  taskCompletion: { weight: 0.3, name: 'Task Completion Rate' },
  codeQuality: { weight: 0.25, name: 'Code Quality' },
  followsInstructions: { weight: 0.2, name: 'Follows Instructions' },
  timeManagement: { weight: 0.15, name: 'Time Management' },
  errorRate: { weight: 0.1, name: 'Error Rate (inverted)' },
};

// Initialize agent performance tracking
function initializeAgentPerformance(agentId) {
  if (!agentPerformance.has(agentId)) {
    agentPerformance.set(agentId, {
      tasksAssigned: 0,
      tasksCompleted: 0,
      tasksSuccessful: 0,
      tasksFailed: 0,
      averageTime: 0,
      totalTime: 0,
      codeQualityScores: [],
      instructionFollowingScores: [],
      errorCount: 0,
      startDate: new Date().toISOString(),
      lastActivity: new Date().toISOString(),
      internshipWeek:
        Math.ceil(
          (Date.now() - new Date().getTime()) / (7 * 24 * 60 * 60 * 1000),
        ) || 1,
      supervisorNotes: [],
    });
  }
}

// Calculate agent grade
function calculateAgentGrade(agentId) {
  const perf = agentPerformance.get(agentId);
  if (!perf || perf.tasksAssigned === 0)
    return {
      grade: 'N/A',
      score: 0,
      info: { name: 'No Data', color: '#6c757d' },
    };

  const completionRate = (perf.tasksCompleted / perf.tasksAssigned) * 100;
  const successRate =
    perf.tasksCompleted > 0
      ? (perf.tasksSuccessful / perf.tasksCompleted) * 100
      : 0;
  const avgCodeQuality =
    perf.codeQualityScores.length > 0
      ? perf.codeQualityScores.reduce((a, b) => a + b, 0) /
        perf.codeQualityScores.length
      : 50;
  const avgInstructionFollowing =
    perf.instructionFollowingScores.length > 0
      ? perf.instructionFollowingScores.reduce((a, b) => a + b, 0) /
        perf.instructionFollowingScores.length
      : 50;
  const errorRate =
    perf.tasksCompleted > 0 ? (perf.errorCount / perf.tasksCompleted) * 100 : 0;
  const timeEfficiency =
    perf.averageTime > 0 ? Math.max(0, 100 - perf.averageTime / 60) : 50;

  const totalScore =
    completionRate * PERFORMANCE_METRICS.taskCompletion.weight +
    avgCodeQuality * PERFORMANCE_METRICS.codeQuality.weight +
    avgInstructionFollowing * PERFORMANCE_METRICS.followsInstructions.weight +
    timeEfficiency * PERFORMANCE_METRICS.timeManagement.weight +
    (100 - errorRate) * PERFORMANCE_METRICS.errorRate.weight;

  // Determine grade
  for (const [grade, info] of Object.entries(GRADE_LEVELS).reverse()) {
    if (totalScore >= info.minScore) {
      return { grade, score: Math.round(totalScore), info };
    }
  }
  return { grade: 'F', score: Math.round(totalScore), info: GRADE_LEVELS.F };
}

// Update agent performance
function updateAgentPerformance(agentId, metrics) {
  initializeAgentPerformance(agentId);
  const perf = agentPerformance.get(agentId);

  if (metrics.taskCompleted) {
    perf.tasksCompleted++;
    perf.lastActivity = new Date().toISOString();

    if (metrics.successful) perf.tasksSuccessful++;
    else perf.tasksFailed++;

    if (metrics.timeSpent) {
      perf.totalTime += metrics.timeSpent;
      perf.averageTime = perf.totalTime / perf.tasksCompleted;
    }

    if (metrics.codeQuality) perf.codeQualityScores.push(metrics.codeQuality);
    if (metrics.instructionFollowing)
      perf.instructionFollowingScores.push(metrics.instructionFollowing);
    if (metrics.hadError) perf.errorCount++;
  }

  if (metrics.taskAssigned) {
    perf.tasksAssigned++;
  }

  // Update grade
  const grade = calculateAgentGrade(agentId);
  agentGrades.set(agentId, grade);

  return grade;
}

// Initialize Redis connection
redisClient.connect().catch(console.error);

// Routes
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// Get all pending approvals
app.get('/api/approvals', (req, res) => {
  const approvals = Array.from(pendingApprovals.values());
  res.json({ success: true, data: approvals });
});

// Approve or reject an agent action
app.post('/api/approvals/:id/decision', async (req, res) => {
  const { id } = req.params;
  const { decision, feedback } = req.body; // decision: 'approve' | 'reject'

  const approval = pendingApprovals.get(id);
  if (!approval) {
    return res
      .status(404)
      .json({ success: false, error: 'Approval not found' });
  }

  try {
    // Update approval status
    approval.status = decision;
    approval.feedback = feedback;
    approval.decidedAt = new Date().toISOString();
    approval.decidedBy = req.user?.id || 'supervisor';

    // Notify n8n workflow
    await axios.post(`${process.env.N8N_API_URL}/webhook/approval-decision`, {
      approvalId: id,
      decision,
      feedback,
      originalRequest: approval,
    });

    // Remove from pending
    pendingApprovals.delete(id);

    // Broadcast to all connected clients
    io.emit('approval-decided', { id, decision, feedback });

    res.json({ success: true, message: `Action ${decision}d successfully` });
  } catch (error) {
    console.error('Error processing approval decision:', error);
    res
      .status(500)
      .json({ success: false, error: 'Failed to process decision' });
  }
});

// Get agent activities and status
app.get('/api/agents', (req, res) => {
  const agents = Array.from(agentActivities.values()).map((agent) => {
    const grade = calculateAgentGrade(agent.id);
    const performance = agentPerformance.get(agent.id) || {};

    return {
      ...agent,
      grade: grade.grade,
      score: grade.score,
      gradeInfo: grade.info,
      performance: {
        tasksCompleted: performance.tasksCompleted || 0,
        tasksAssigned: performance.tasksAssigned || 0,
        successRate:
          performance.tasksCompleted > 0
            ? Math.round(
                (performance.tasksSuccessful / performance.tasksCompleted) *
                  100,
              )
            : 0,
        internshipWeek: performance.internshipWeek || 1,
        averageTime: Math.round(performance.averageTime || 0),
        errorCount: performance.errorCount || 0,
      },
    };
  });
  res.json({ success: true, data: agents });
});

// Get detailed agent performance
app.get('/api/agents/:id/performance', (req, res) => {
  const agentId = req.params.id;
  const performance = agentPerformance.get(agentId);
  const grade = calculateAgentGrade(agentId);

  if (!performance) {
    return res
      .status(404)
      .json({ success: false, error: 'Agent performance not found' });
  }

  res.json({
    success: true,
    data: {
      ...performance,
      grade: grade.grade,
      score: grade.score,
      gradeInfo: grade.info,
      metrics: {
        completionRate:
          performance.tasksAssigned > 0
            ? Math.round(
                (performance.tasksCompleted / performance.tasksAssigned) * 100,
              )
            : 0,
        successRate:
          performance.tasksCompleted > 0
            ? Math.round(
                (performance.tasksSuccessful / performance.tasksCompleted) *
                  100,
              )
            : 0,
        avgCodeQuality:
          performance.codeQualityScores.length > 0
            ? Math.round(
                performance.codeQualityScores.reduce((a, b) => a + b, 0) /
                  performance.codeQualityScores.length,
              )
            : 0,
        avgInstructionFollowing:
          performance.instructionFollowingScores.length > 0
            ? Math.round(
                performance.instructionFollowingScores.reduce(
                  (a, b) => a + b,
                  0,
                ) / performance.instructionFollowingScores.length,
              )
            : 0,
      },
    },
  });
});

// Add supervisor notes for agent
app.post('/api/agents/:id/notes', (req, res) => {
  const agentId = req.params.id;
  const { note, rating } = req.body;

  initializeAgentPerformance(agentId);
  const performance = agentPerformance.get(agentId);

  const supervisorNote = {
    id: Date.now().toString(),
    note,
    rating: rating || null,
    timestamp: new Date().toISOString(),
    supervisor: req.user?.id || 'supervisor',
  };

  performance.supervisorNotes.push(supervisorNote);

  // Update performance based on rating
  if (rating) {
    if (rating >= 80) performance.instructionFollowingScores.push(rating);
    if (rating >= 70) performance.codeQualityScores.push(rating);
  }

  const grade = updateAgentPerformance(agentId, {});

  res.json({ success: true, data: supervisorNote, grade });
});

// Emergency stop all agents
app.post('/api/emergency-stop', async (req, res) => {
  try {
    // Notify all agents to stop
    await axios.post(`${process.env.N8N_API_URL}/webhook/emergency-stop`, {
      timestamp: new Date().toISOString(),
      reason: req.body.reason || 'Manual emergency stop',
    });

    // Clear all pending approvals
    pendingApprovals.clear();

    // Broadcast emergency stop
    io.emit('emergency-stop', { reason: req.body.reason });

    res.json({ success: true, message: 'Emergency stop initiated' });
  } catch (error) {
    console.error('Error during emergency stop:', error);
    res
      .status(500)
      .json({ success: false, error: 'Failed to initiate emergency stop' });
  }
});

// Webhook endpoint for n8n to request approvals
app.post('/webhook/request-approval', (req, res) => {
  const {
    agentId,
    action,
    description,
    confidence,
    affectedFiles,
    riskLevel,
    estimatedImpact,
  } = req.body;

  // Generate unique approval ID
  const approvalId = `approval_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

  // Create approval request
  const approval = {
    id: approvalId,
    agentId,
    action,
    description,
    confidence: Number.parseFloat(confidence),
    affectedFiles: affectedFiles || [],
    riskLevel: riskLevel || 'medium',
    estimatedImpact,
    status: 'pending',
    createdAt: new Date().toISOString(),
    requiresApproval: confidence < confidenceThreshold || riskLevel === 'high',
  };

  // Store approval request
  pendingApprovals.set(approvalId, approval);

  // Broadcast to all connected supervisors
  io.emit('new-approval-request', approval);

  // Log for audit
  console.log(
    `New approval request: ${approvalId} - ${action} (confidence: ${confidence})`,
  );

  res.json({
    success: true,
    approvalId,
    requiresApproval: approval.requiresApproval,
  });
});

// Webhook endpoint for agent status updates
app.post('/webhook/agent-status', (req, res) => {
  const { agentId, status, currentTask, progress, lastActivity } = req.body;

  const agent = {
    id: agentId,
    status,
    currentTask,
    progress: Number.parseFloat(progress) || 0,
    lastActivity: lastActivity || new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };

  agentActivities.set(agentId, agent);

  // Initialize performance tracking for new agents
  initializeAgentPerformance(agentId);

  // Broadcast agent status update
  io.emit('agent-status-update', agent);

  res.json({ success: true });
});

// Webhook endpoint for agent performance updates
app.post('/webhook/agent-performance', (req, res) => {
  const {
    agentId,
    taskCompleted,
    successful,
    timeSpent,
    codeQuality,
    instructionFollowing,
    hadError,
    taskAssigned,
    taskDetails,
  } = req.body;

  const grade = updateAgentPerformance(agentId, {
    taskCompleted,
    successful,
    timeSpent,
    codeQuality,
    instructionFollowing,
    hadError,
    taskAssigned,
  });

  // Log performance update
  console.log(
    `Agent ${agentId} performance updated: Grade ${grade.grade} (${grade.score})`,
  );

  // Broadcast performance update
  io.emit('agent-performance-update', {
    agentId,
    grade: grade.grade,
    score: grade.score,
    performance: agentPerformance.get(agentId),
  });

  res.json({ success: true, grade });
});

// Socket.IO connection handling
io.on('connection', (socket) => {
  console.log('Supervisor connected:', socket.id);

  // Send current state to new connection
  socket.emit('initial-state', {
    pendingApprovals: Array.from(pendingApprovals.values()),
    agentActivities: Array.from(agentActivities.values()),
  });

  socket.on('disconnect', () => {
    console.log('Supervisor disconnected:', socket.id);
  });
});

// Error handling
app.use((error, req, res, next) => {
  console.error('Server error:', error);
  res.status(500).json({ success: false, error: 'Internal server error' });
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    pendingApprovals: pendingApprovals.size,
    activeAgents: agentActivities.size,
  });
});

// Start server
const PORT = process.env.PORT || 3001;
server.listen(PORT, () => {
  console.log(`AI Agent Supervisor running on port ${PORT}`);
  console.log(`Confidence threshold: ${confidenceThreshold}`);
  console.log(
    `Human approval required: ${process.env.HUMAN_APPROVAL_REQUIRED !== 'false'}`,
  );
});
