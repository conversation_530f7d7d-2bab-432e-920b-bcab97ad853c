#!/usr/bin/env node

const axios = require('axios');

// SDLC Agent Specializations with Sprint-based workflows
const AGENT_SPECIALIZATIONS = {
  'requirements-analyst': {
    name: 'Requirements Analyst Agent',
    domain: 'Business Analysis',
    sdlcPhase: 'Requirements',
    sprintTasks: [
      'Analyze user stories and acceptance criteria',
      'Create functional requirements documentation',
      'Validate business rules and constraints',
      'Review and refine requirements with stakeholders',
    ],
    complexity: [1, 2, 3],
    tools: ['documentation', 'analysis', 'validation'],
  },
  'frontend-developer': {
    name: 'Frontend Developer Agent',
    domain: 'User Interface Development',
    sdlcPhase: 'Development',
    sprintTasks: [
      'Implement UI components and layouts',
      'Add responsive design and accessibility',
      'Integrate with backend APIs',
      'Write frontend unit tests',
      'Optimize performance and user experience',
    ],
    complexity: [1, 2, 3, 4],
    tools: ['react', 'css', 'javascript', 'testing'],
  },
  'backend-developer': {
    name: 'Backend Developer Agent',
    domain: 'Server-side Development',
    sdlcPhase: 'Development',
    sprintTasks: [
      'Design and implement API endpoints',
      'Create database schemas and migrations',
      'Implement business logic and validation',
      'Add authentication and authorization',
      'Write integration tests',
    ],
    complexity: [2, 3, 4, 5],
    tools: ['nodejs', 'database', 'api', 'testing'],
  },
  'qa-tester': {
    name: 'QA Testing Agent',
    domain: 'Quality Assurance',
    sdlcPhase: 'Testing',
    sprintTasks: [
      'Create test plans and test cases',
      'Execute manual and automated tests',
      'Report and track defects',
      'Perform regression testing',
      'Validate user acceptance criteria',
    ],
    complexity: [1, 2, 3],
    tools: ['testing', 'automation', 'reporting'],
  },
  'devops-engineer': {
    name: 'DevOps Engineer Agent',
    domain: 'Infrastructure & Deployment',
    sdlcPhase: 'Deployment',
    sprintTasks: [
      'Set up CI/CD pipelines',
      'Configure infrastructure and monitoring',
      'Manage deployments and rollbacks',
      'Optimize performance and scalability',
      'Ensure security and compliance',
    ],
    complexity: [3, 4, 5],
    tools: ['docker', 'kubernetes', 'monitoring', 'security'],
  },
  'documentation-specialist': {
    name: 'Documentation Specialist Agent',
    domain: 'Technical Writing',
    sdlcPhase: 'Documentation',
    sprintTasks: [
      'Create technical documentation',
      'Write API documentation and guides',
      'Update user manuals and help content',
      'Review and improve existing docs',
      'Create diagrams and visual aids',
    ],
    complexity: [1, 2],
    tools: ['markdown', 'diagrams', 'writing'],
  },
};

// Create SDLC workflow for each agent type
async function createSDLCWorkflow(agentType, specialization) {
  const workflowName = `SDLC ${specialization.name} Workflow`;

  const workflow = {
    name: workflowName,
    nodes: [
      // 1. Webhook Trigger - Task Assignment
      {
        id: 'webhook-trigger',
        name: 'Task Assignment Webhook',
        type: 'n8n-nodes-base.webhook',
        typeVersion: 1,
        position: [100, 200],
        parameters: {
          path: `sdlc-${agentType}`,
          httpMethod: 'POST',
          responseMode: 'responseNode',
        },
      },

      // 2. Agent Status Update - Working
      {
        id: 'status-working',
        name: 'Update Status: Working',
        type: 'n8n-nodes-base.httpRequest',
        typeVersion: 3,
        position: [300, 200],
        parameters: {
          url: 'http://localhost:3001/webhook/agent-status',
          method: 'POST',
          sendBody: true,
          bodyParameters: {
            parameters: [
              {
                name: 'agentId',
                value: `agent-${agentType}-{{ $json.agentId || '001' }}`,
              },
              { name: 'status', value: 'working' },
              { name: 'currentTask', value: '{{ $json.task }}' },
              { name: 'progress', value: '0' },
              { name: 'lastActivity', value: '{{ $now }}' },
            ],
          },
        },
      },

      // 3. Task Analysis - Gemma 3 AI
      {
        id: 'task-analysis',
        name: 'Analyze Task with Gemma 3',
        type: 'n8n-nodes-base.httpRequest',
        typeVersion: 3,
        position: [500, 200],
        parameters: {
          url: 'https://generativelanguage.googleapis.com/v1beta/models/gemma-3n-e4b-it:generateContent',
          method: 'POST',
          sendHeaders: true,
          headerParameters: {
            parameters: [
              { name: 'Content-Type', value: 'application/json' },
              { name: 'x-goog-api-key', value: '{{ $env.GEMINI_API_KEY }}' },
            ],
          },
          sendBody: true,
          bodyParameters: {
            parameters: [
              {
                name: 'contents',
                value: JSON.stringify([
                  {
                    parts: [
                      {
                        text: `You are a ${specialization.name} in the ${specialization.sdlcPhase} phase of SDLC.

Task: {{ $json.task }}
Description: {{ $json.description }}
Priority: {{ $json.priority }}
Complexity: {{ $json.complexity }}

Sprint Tasks for ${specialization.name}:
${specialization.sprintTasks.map((task, i) => `${i + 1}. ${task}`).join('\n')}

Analyze this task and provide:
1. Estimated effort (1-5 story points)
2. Required sprint activities
3. Dependencies and risks
4. Acceptance criteria
5. Implementation approach

Respond in JSON format:
{
  "effort": 2,
  "sprintActivities": ["activity1", "activity2"],
  "dependencies": ["dep1"],
  "risks": ["risk1"],
  "acceptanceCriteria": ["criteria1"],
  "approach": "implementation approach",
  "confidence": 0.8
}`,
                      },
                    ],
                  },
                ]),
              },
            ],
          },
        },
      },

      // 4. Progress Update - 25%
      {
        id: 'progress-25',
        name: 'Progress: 25% - Analysis Complete',
        type: 'n8n-nodes-base.httpRequest',
        typeVersion: 3,
        position: [700, 200],
        parameters: {
          url: 'http://localhost:3001/webhook/agent-status',
          method: 'POST',
          sendBody: true,
          bodyParameters: {
            parameters: [
              {
                name: 'agentId',
                value: `agent-${agentType}-{{ $('webhook-trigger').item.json.agentId || '001' }}`,
              },
              { name: 'status', value: 'working' },
              {
                name: 'currentTask',
                value: '{{ $("webhook-trigger").item.json.task }}',
              },
              { name: 'progress', value: '25' },
              { name: 'lastActivity', value: '{{ $now }}' },
            ],
          },
        },
      },

      // 5. Sprint Planning
      {
        id: 'sprint-planning',
        name: 'Sprint Planning & Task Breakdown',
        type: 'n8n-nodes-base.code',
        typeVersion: 2,
        position: [900, 200],
        parameters: {
          language: 'javascript',
          jsCode: `
// Parse AI analysis
const analysis = JSON.parse($input.item.json.candidates[0].content.parts[0].text);
const originalTask = $('webhook-trigger').item.json;

// Create sprint breakdown
const sprintPlan = {
  taskId: originalTask.taskId || 'task-' + Date.now(),
  agentType: '${agentType}',
  specialization: '${specialization.name}',
  sdlcPhase: '${specialization.sdlcPhase}',
  effort: analysis.effort,
  sprintActivities: analysis.sprintActivities,
  dependencies: analysis.dependencies,
  risks: analysis.risks,
  acceptanceCriteria: analysis.acceptanceCriteria,
  approach: analysis.approach,
  confidence: analysis.confidence,
  startTime: new Date().toISOString(),
  estimatedCompletion: new Date(Date.now() + (analysis.effort * 3600000)).toISOString() // effort in hours
};

return { sprintPlan, originalTask, analysis };`,
        },
      },

      // 6. Progress Update - 50%
      {
        id: 'progress-50',
        name: 'Progress: 50% - Planning Complete',
        type: 'n8n-nodes-base.httpRequest',
        typeVersion: 3,
        position: [1100, 200],
        parameters: {
          url: 'http://localhost:3001/webhook/agent-status',
          method: 'POST',
          sendBody: true,
          bodyParameters: {
            parameters: [
              {
                name: 'agentId',
                value: `agent-${agentType}-{{ $('webhook-trigger').item.json.agentId || '001' }}`,
              },
              { name: 'status', value: 'working' },
              {
                name: 'currentTask',
                value: '{{ $("webhook-trigger").item.json.task }}',
              },
              { name: 'progress', value: '50' },
              { name: 'lastActivity', value: '{{ $now }}' },
            ],
          },
        },
      },

      // 7. Implementation Phase
      {
        id: 'implementation',
        name: 'SDLC Implementation Phase',
        type: 'n8n-nodes-base.code',
        typeVersion: 2,
        position: [1300, 200],
        parameters: {
          language: 'javascript',
          jsCode: `
// Simulate SDLC implementation based on agent specialization
const sprintPlan = $input.item.json.sprintPlan;
const agentType = '${agentType}';

// Simulate realistic work with some variability
const workQuality = Math.random() * 40 + 60; // 60-100
const instructionFollowing = Math.random() * 30 + 70; // 70-100
const hasError = Math.random() < 0.2; // 20% chance of error
const timeSpent = sprintPlan.effort * (0.8 + Math.random() * 0.4); // ±20% variance

// Domain-specific implementation results
const implementationResults = {
  '${agentType}': {
    deliverables: ${JSON.stringify(specialization.sprintTasks)},
    quality: Math.round(workQuality),
    instructionFollowing: Math.round(instructionFollowing),
    timeSpent: Math.round(timeSpent * 3600), // in seconds
    hasError: hasError,
    completed: !hasError || Math.random() > 0.5,
    artifacts: []
  }
};

return { 
  ...sprintPlan, 
  implementation: implementationResults['${agentType}'],
  completedAt: new Date().toISOString()
};`,
        },
      },

      // 8. Progress Update - 75%
      {
        id: 'progress-75',
        name: 'Progress: 75% - Implementation Complete',
        type: 'n8n-nodes-base.httpRequest',
        typeVersion: 3,
        position: [1500, 200],
        parameters: {
          url: 'http://localhost:3001/webhook/agent-status',
          method: 'POST',
          sendBody: true,
          bodyParameters: {
            parameters: [
              {
                name: 'agentId',
                value: `agent-${agentType}-{{ $('webhook-trigger').item.json.agentId || '001' }}`,
              },
              { name: 'status', value: 'working' },
              {
                name: 'currentTask',
                value: '{{ $("webhook-trigger").item.json.task }}',
              },
              { name: 'progress', value: '75' },
              { name: 'lastActivity', value: '{{ $now }}' },
            ],
          },
        },
      },

      // 9. Performance Tracking
      {
        id: 'performance-update',
        name: 'Update Agent Performance',
        type: 'n8n-nodes-base.httpRequest',
        typeVersion: 3,
        position: [1700, 200],
        parameters: {
          url: 'http://localhost:3001/webhook/agent-performance',
          method: 'POST',
          sendBody: true,
          bodyParameters: {
            parameters: [
              {
                name: 'agentId',
                value: `agent-${agentType}-{{ $('webhook-trigger').item.json.agentId || '001' }}`,
              },
              { name: 'taskCompleted', value: 'true' },
              {
                name: 'successful',
                value: '{{ $json.implementation.completed }}',
              },
              {
                name: 'timeSpent',
                value: '{{ $json.implementation.timeSpent }}',
              },
              {
                name: 'codeQuality',
                value: '{{ $json.implementation.quality }}',
              },
              {
                name: 'instructionFollowing',
                value: '{{ $json.implementation.instructionFollowing }}',
              },
              {
                name: 'hadError',
                value: '{{ $json.implementation.hasError }}',
              },
              { name: 'taskDetails', value: '{{ JSON.stringify($json) }}' },
            ],
          },
        },
      },

      // 10. Final Status Update - Complete
      {
        id: 'status-complete',
        name: 'Update Status: Complete',
        type: 'n8n-nodes-base.httpRequest',
        typeVersion: 3,
        position: [1900, 200],
        parameters: {
          url: 'http://localhost:3001/webhook/agent-status',
          method: 'POST',
          sendBody: true,
          bodyParameters: {
            parameters: [
              {
                name: 'agentId',
                value: `agent-${agentType}-{{ $('webhook-trigger').item.json.agentId || '001' }}`,
              },
              { name: 'status', value: 'idle' },
              { name: 'currentTask', value: 'null' },
              { name: 'progress', value: '100' },
              { name: 'lastActivity', value: '{{ $now }}' },
            ],
          },
        },
      },

      // 11. Response
      {
        id: 'webhook-response',
        name: 'Webhook Response',
        type: 'n8n-nodes-base.respondToWebhook',
        typeVersion: 1,
        position: [2100, 200],
        parameters: {
          respondWith: 'json',
          responseBody:
            '{{ JSON.stringify({ success: true, agentType: "' +
            agentType +
            '", taskCompleted: $json.implementation.completed, grade: "TBD" }) }}',
        },
      },
    ],
    connections: {
      'webhook-trigger': { main: [['status-working']] },
      'status-working': { main: [['task-analysis']] },
      'task-analysis': { main: [['progress-25']] },
      'progress-25': { main: [['sprint-planning']] },
      'sprint-planning': { main: [['progress-50']] },
      'progress-50': { main: [['implementation']] },
      implementation: { main: [['progress-75']] },
      'progress-75': { main: [['performance-update']] },
      'performance-update': { main: [['status-complete']] },
      'status-complete': { main: [['webhook-response']] },
    },
    active: true,
    settings: {
      executionOrder: 'v1',
    },
  };

  return workflow;
}

// Create all SDLC workflows
async function createAllSDLCWorkflows() {
  console.log('🏗️  Creating SDLC-based Agent Workflows...\n');

  const createdWorkflows = [];

  for (const [agentType, specialization] of Object.entries(
    AGENT_SPECIALIZATIONS,
  )) {
    try {
      console.log(`📋 Creating workflow for: ${specialization.name}`);

      const workflow = await createSDLCWorkflow(agentType, specialization);

      // Create workflow in n8n using MCP
      // This would be done via the n8n MCP tools
      console.log(`   ✅ Workflow structure created for ${agentType}`);
      console.log(`   📊 SDLC Phase: ${specialization.sdlcPhase}`);
      console.log(`   🎯 Sprint Tasks: ${specialization.sprintTasks.length}`);
      console.log(
        `   🔧 Complexity Range: ${specialization.complexity.join('-')}`,
      );

      createdWorkflows.push({
        agentType,
        specialization,
        workflow,
        webhookPath: `sdlc-${agentType}`,
      });
    } catch (error) {
      console.error(
        `❌ Failed to create workflow for ${agentType}:`,
        error.message,
      );
    }
  }

  return createdWorkflows;
}

// Export for use
module.exports = {
  AGENT_SPECIALIZATIONS,
  createSDLCWorkflow,
  createAllSDLCWorkflows,
};

// Run if called directly
if (require.main === module) {
  createAllSDLCWorkflows()
    .then((workflows) => {
      console.log(`\n🎉 Created ${workflows.length} SDLC workflows!`);
      console.log('\n📋 Webhook endpoints:');
      workflows.forEach((wf) => {
        console.log(
          `   ${wf.specialization.name}: http://localhost:5678/webhook/${wf.webhookPath}`,
        );
      });
    })
    .catch(console.error);
}
