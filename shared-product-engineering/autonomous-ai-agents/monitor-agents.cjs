#!/usr/bin/env node

const axios = require('axios');
const fs = require('fs');

// Configuration
const SUPERVISOR_URL = 'http://localhost:3001';
const TASK_API_URL = 'http://localhost:3003';
const MONITOR_INTERVAL = 3000; // 3 seconds
const LOG_FILE = 'agent-monitoring.log';

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
};

// Agent status tracking
const previousAgentStates = new Map();
const monitoringStartTime = new Date();
let totalTasksObserved = 0;

// Log function
function log(message, level = 'INFO') {
  const timestamp = new Date().toISOString();
  const logEntry = `[${timestamp}] ${level}: ${message}`;

  console.log(logEntry);

  // Append to log file
  fs.appendFileSync(LOG_FILE, logEntry + '\n');
}

// Format agent status with colors
function formatAgentStatus(agent) {
  const statusColors = {
    idle: colors.cyan,
    working: colors.yellow,
    completed: colors.green,
    error: colors.red,
    stopped: colors.magenta,
  };

  const gradeColors = {
    A: colors.green,
    B: colors.cyan,
    C: colors.yellow,
    D: colors.magenta,
    F: colors.red,
    'N/A': colors.white,
  };

  const statusColor = statusColors[agent.status] || colors.white;
  const gradeColor = gradeColors[agent.grade] || colors.white;

  let output = `${colors.bright}${agent.id}${colors.reset} (${agent.grade ? agent.id.split('-')[2] : 'Unknown'}):\n`;
  output += `  Status: ${statusColor}${agent.status.toUpperCase()}${colors.reset}`;
  output += ` | Grade: ${gradeColor}${agent.grade} (${agent.score}/100)${colors.reset}`;
  output += ` | Tasks: ${agent.performance.tasksCompleted}/${agent.performance.tasksAssigned}`;
  output += ` | Success: ${agent.performance.successRate}%\n`;

  if (agent.currentTask) {
    const taskPreview =
      agent.currentTask.length > 60
        ? agent.currentTask.substring(0, 60) + '...'
        : agent.currentTask;
    output += `  ${colors.blue}Current Task:${colors.reset} ${taskPreview}\n`;
    output += `  ${colors.blue}Progress:${colors.reset} ${Math.round(agent.progress)}%`;

    // Progress bar
    const barLength = 20;
    const filledLength = Math.round((agent.progress / 100) * barLength);
    const bar = '█'.repeat(filledLength) + '░'.repeat(barLength - filledLength);
    output += ` [${colors.green}${bar}${colors.reset}]\n`;
  }

  if (agent.performance.averageTime > 0) {
    output += `  ${colors.blue}Avg Time:${colors.reset} ${agent.performance.averageTime}s`;
    output += ` | ${colors.blue}Errors:${colors.reset} ${agent.performance.errorCount}`;
    output += ` | ${colors.blue}Week:${colors.reset} ${agent.performance.internshipWeek}\n`;
  }

  return output;
}

// Detect agent state changes
function detectStateChanges(currentAgents) {
  const changes = [];

  for (const agent of currentAgents) {
    const previous = previousAgentStates.get(agent.id);

    if (!previous) {
      changes.push({
        type: 'NEW_AGENT',
        agent: agent,
        message: `New agent registered: ${agent.id}`,
      });
    } else {
      // Check for status changes
      if (previous.status !== agent.status) {
        changes.push({
          type: 'STATUS_CHANGE',
          agent: agent,
          previous: previous.status,
          current: agent.status,
          message: `${agent.id} status: ${previous.status} → ${agent.status}`,
        });
      }

      // Check for task changes
      if (previous.currentTask !== agent.currentTask) {
        if (agent.currentTask && !previous.currentTask) {
          changes.push({
            type: 'TASK_STARTED',
            agent: agent,
            message: `${agent.id} started task: ${agent.currentTask.substring(0, 50)}...`,
          });
          totalTasksObserved++;
        } else if (!agent.currentTask && previous.currentTask) {
          changes.push({
            type: 'TASK_COMPLETED',
            agent: agent,
            message: `${agent.id} completed task: ${previous.currentTask.substring(0, 50)}...`,
          });
        }
      }

      // Check for grade changes
      if (previous.grade !== agent.grade) {
        const direction =
          agent.score > previous.score
            ? '↗️'
            : agent.score < previous.score
              ? '↘️'
              : '→';
        changes.push({
          type: 'GRADE_CHANGE',
          agent: agent,
          message: `${agent.id} grade: ${previous.grade} (${previous.score}) ${direction} ${agent.grade} (${agent.score})`,
        });
      }

      // Check for significant progress changes
      if (
        agent.currentTask &&
        Math.abs(agent.progress - previous.progress) >= 20
      ) {
        changes.push({
          type: 'PROGRESS_UPDATE',
          agent: agent,
          message: `${agent.id} progress: ${Math.round(previous.progress)}% → ${Math.round(agent.progress)}%`,
        });
      }
    }

    // Update previous state
    previousAgentStates.set(agent.id, {
      status: agent.status,
      currentTask: agent.currentTask,
      progress: agent.progress,
      grade: agent.grade,
      score: agent.score,
      performance: { ...agent.performance },
    });
  }

  return changes;
}

// Get agent data
async function getAgentData() {
  try {
    const response = await axios.get(`${SUPERVISOR_URL}/api/agents`);
    if (response.data.success) {
      return response.data.data;
    }
    return [];
  } catch (error) {
    log(`Error fetching agent data: ${error.message}`, 'ERROR');
    return [];
  }
}

// Get task statistics
async function getTaskStats() {
  try {
    const response = await axios.get(`${TASK_API_URL}/api/stats`);
    if (response.data.success) {
      return response.data.results;
    }
    return null;
  } catch (error) {
    log(`Error fetching task stats: ${error.message}`, 'ERROR');
    return null;
  }
}

// Display monitoring dashboard
function displayDashboard(agents, taskStats, changes) {
  // Clear screen
  console.clear();

  // Header
  console.log(
    `${colors.bright}${colors.cyan}🤖 AI AGENT INTERNSHIP MONITORING DASHBOARD${colors.reset}`,
  );
  console.log(
    `${colors.bright}═══════════════════════════════════════════════════════════════${colors.reset}`,
  );

  const uptime = Math.round(
    (Date.now() - monitoringStartTime.getTime()) / 1000,
  );
  console.log(
    `${colors.blue}Monitoring Time:${colors.reset} ${uptime}s | ${colors.blue}Tasks Observed:${colors.reset} ${totalTasksObserved} | ${colors.blue}Last Update:${colors.reset} ${new Date().toLocaleTimeString()}\n`,
  );

  // Task Statistics
  if (taskStats) {
    console.log(`${colors.bright}📊 TASK STATISTICS${colors.reset}`);
    console.log(
      `Total Tasks: ${taskStats.totalTasks} | Pending: ${taskStats.pendingTasks} | Completed: ${taskStats.completedTasks}`,
    );
    console.log(
      `High Priority: ${taskStats.highPriorityTasks} | High Risk: ${taskStats.highRiskTasks} | Complex: ${taskStats.complexTasks}\n`,
    );
  }

  // Recent Changes
  if (changes.length > 0) {
    console.log(`${colors.bright}🔔 RECENT ACTIVITY${colors.reset}`);
    changes.slice(-5).forEach((change) => {
      const typeColors = {
        NEW_AGENT: colors.green,
        STATUS_CHANGE: colors.yellow,
        TASK_STARTED: colors.cyan,
        TASK_COMPLETED: colors.green,
        GRADE_CHANGE: colors.magenta,
        PROGRESS_UPDATE: colors.blue,
      };
      const color = typeColors[change.type] || colors.white;
      console.log(`  ${color}${change.type}:${colors.reset} ${change.message}`);
    });
    console.log('');
  }

  // Agent Status
  console.log(
    `${colors.bright}👥 AGENT STATUS (${agents.length} agents)${colors.reset}`,
  );
  console.log(
    `${colors.bright}─────────────────────────────────────────────────────────────${colors.reset}`,
  );

  if (agents.length === 0) {
    console.log(
      `${colors.yellow}No agents currently registered${colors.reset}`,
    );
  } else {
    agents.forEach((agent) => {
      console.log(formatAgentStatus(agent));
    });
  }

  // Summary
  const workingAgents = agents.filter((a) => a.status === 'working').length;
  const idleAgents = agents.filter((a) => a.status === 'idle').length;
  const totalTasks = agents.reduce(
    (sum, a) => sum + a.performance.tasksCompleted,
    0,
  );
  const avgGrade =
    agents.length > 0
      ? agents.filter((a) => a.score > 0).reduce((sum, a) => sum + a.score, 0) /
        agents.filter((a) => a.score > 0).length
      : 0;

  console.log(`${colors.bright}📈 SUMMARY${colors.reset}`);
  console.log(
    `Working: ${workingAgents} | Idle: ${idleAgents} | Total Tasks Completed: ${totalTasks} | Avg Grade: ${Math.round(avgGrade)}/100`,
  );

  console.log(
    `\n${colors.bright}Press Ctrl+C to stop monitoring${colors.reset}`,
  );
}

// Start continuous monitoring
async function startMonitoring() {
  log('Starting AI Agent monitoring...', 'INFO');
  console.log(
    `${colors.green}🚀 Starting AI Agent Monitoring Dashboard${colors.reset}`,
  );
  console.log(
    `${colors.blue}Monitoring interval: ${MONITOR_INTERVAL}ms${colors.reset}`,
  );
  console.log(`${colors.blue}Log file: ${LOG_FILE}${colors.reset}\n`);

  // Initial clear of log file
  fs.writeFileSync(
    LOG_FILE,
    `AI Agent Monitoring Started: ${new Date().toISOString()}\n`,
  );

  const monitorLoop = async () => {
    try {
      // Get current data
      const agents = await getAgentData();
      const taskStats = await getTaskStats();

      // Detect changes
      const changes = detectStateChanges(agents);

      // Log significant changes
      changes.forEach((change) => {
        log(change.message, change.type);
      });

      // Display dashboard
      displayDashboard(agents, taskStats, changes);
    } catch (error) {
      log(`Monitoring error: ${error.message}`, 'ERROR');
    }

    // Schedule next update
    setTimeout(monitorLoop, MONITOR_INTERVAL);
  };

  // Start monitoring loop
  monitorLoop();
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  log('Monitoring stopped by user', 'INFO');
  console.log(
    `\n${colors.green}Monitoring stopped. Check ${LOG_FILE} for full log.${colors.reset}`,
  );
  process.exit(0);
});

// Start monitoring if run directly
if (require.main === module) {
  startMonitoring().catch(console.error);
}

module.exports = { startMonitoring, getAgentData };
