#!/usr/bin/env node

const axios = require('axios');

// Configuration
const N8N_API_URL = 'http://localhost:5678';

// Activate all workflows
async function activateAllWorkflows() {
  console.log('🔧 Activating all n8n workflows...\n');

  try {
    // Get all workflows
    const response = await axios.get(`${N8N_API_URL}/api/v1/workflows`);
    const workflows = response.data;

    console.log(`📋 Found ${workflows.length} workflows to activate:`);

    const activationResults = [];

    for (const workflow of workflows) {
      try {
        console.log(`🔄 Activating: ${workflow.name} (${workflow.id})`);

        if (workflow.active) {
          console.log('   ✅ Already active');
          activationResults.push({
            workflow,
            success: true,
            alreadyActive: true,
          });
          continue;
        }

        // Activate the workflow
        const activateResponse = await axios.patch(
          `${N8N_API_URL}/api/v1/workflows/${workflow.id}/activate`,
          { active: true },
        );

        if (activateResponse.status === 200) {
          console.log('   ✅ Successfully activated');
          activationResults.push({
            workflow,
            success: true,
            alreadyActive: false,
          });
        } else {
          console.log(
            `   ⚠️  Activation may have failed (status: ${activateResponse.status})`,
          );
          activationResults.push({
            workflow,
            success: false,
            error: `HTTP ${activateResponse.status}`,
          });
        }
      } catch (error) {
        console.log(`   ❌ Failed to activate: ${error.message}`);
        activationResults.push({
          workflow,
          success: false,
          error: error.message,
        });
      }

      // Small delay between activations
      await new Promise((resolve) => setTimeout(resolve, 500));
    }

    // Summary
    console.log('\n📊 ACTIVATION SUMMARY:');
    const successful = activationResults.filter((r) => r.success).length;
    const alreadyActive = activationResults.filter(
      (r) => r.success && r.alreadyActive,
    ).length;
    const newlyActivated = activationResults.filter(
      (r) => r.success && !r.alreadyActive,
    ).length;
    const failed = activationResults.filter((r) => !r.success).length;

    console.log(`✅ Total successful: ${successful}`);
    console.log(`🔄 Already active: ${alreadyActive}`);
    console.log(`🆕 Newly activated: ${newlyActivated}`);
    console.log(`❌ Failed: ${failed}`);

    if (failed > 0) {
      console.log('\n❌ Failed workflows:');
      activationResults
        .filter((r) => !r.success)
        .forEach((result) => {
          console.log(`   - ${result.workflow.name}: ${result.error}`);
        });
    }

    console.log('\n🎉 Workflow activation complete!');

    return activationResults;
  } catch (error) {
    console.error('❌ Failed to activate workflows:', error.message);
    return [];
  }
}

// Test workflow connectivity
async function testWorkflowConnectivity() {
  console.log('\n🧪 Testing workflow connectivity...\n');

  const testEndpoints = [
    {
      name: 'Markdown Analyzer',
      url: 'http://localhost:3003/api/stats',
      description: 'Task discovery service',
    },
    {
      name: 'Supervisor UI',
      url: 'http://localhost:3001/api/agents',
      description: 'Agent monitoring service',
    },
    {
      name: 'n8n',
      url: 'http://localhost:5678',
      description: 'Workflow orchestration',
    },
  ];

  const connectivityResults = [];

  for (const endpoint of testEndpoints) {
    try {
      console.log(`🔍 Testing ${endpoint.name}...`);
      const response = await axios.get(endpoint.url, { timeout: 5000 });
      console.log(`   ✅ ${endpoint.name}: Connected (${response.status})`);
      connectivityResults.push({
        endpoint,
        success: true,
        status: response.status,
      });
    } catch (error) {
      console.log(`   ❌ ${endpoint.name}: ${error.message}`);
      connectivityResults.push({
        endpoint,
        success: false,
        error: error.message,
      });
    }
  }

  const connectedServices = connectivityResults.filter((r) => r.success).length;
  console.log(
    `\n📊 Connectivity: ${connectedServices}/${testEndpoints.length} services reachable`,
  );

  return connectivityResults;
}

// Trigger a test workflow execution
async function triggerTestExecution() {
  console.log('\n🚀 Triggering test workflow execution...\n');

  try {
    // Test the frontend agent workflow
    const testPayload = {
      task: 'Test task: Add responsive navigation component',
      description: 'Integration test task',
      priority: 'low',
      complexity: 1,
      taskId: 'test-' + Date.now(),
      agentType: 'frontend-developer',
    };

    console.log('📋 Sending test task to Frontend Developer Agent...');
    const response = await axios.post(
      'http://localhost:5678/webhook/frontend-agent',
      testPayload,
      {
        timeout: 30000, // 30 second timeout for workflow execution
      },
    );

    if (response.status === 200) {
      console.log('✅ Test execution successful!');
      console.log(`   Response: ${JSON.stringify(response.data, null, 2)}`);
      return { success: true, response: response.data };
    }
    console.log(`⚠️  Test execution completed with status: ${response.status}`);
    return { success: false, error: `HTTP ${response.status}` };
  } catch (error) {
    console.log(`❌ Test execution failed: ${error.message}`);
    return { success: false, error: error.message };
  }
}

// Main function
async function main() {
  console.log('🎯 n8n Workflow Activation and Testing\n');

  // 1. Test connectivity first
  const connectivity = await testWorkflowConnectivity();
  const allServicesConnected = connectivity.every((r) => r.success);

  if (!allServicesConnected) {
    console.log(
      '\n⚠️  Some services are not reachable. Workflows may not function properly.',
    );
    console.log('   Make sure all Docker services are running.');
  }

  // 2. Activate all workflows
  const activationResults = await activateAllWorkflows();
  const activatedCount = activationResults.filter((r) => r.success).length;

  if (activatedCount === 0) {
    console.log(
      '\n❌ No workflows were activated. Cannot proceed with testing.',
    );
    return;
  }

  // 3. Wait a moment for workflows to initialize
  console.log('\n⏳ Waiting for workflows to initialize...');
  await new Promise((resolve) => setTimeout(resolve, 3000));

  // 4. Trigger test execution
  const testResult = await triggerTestExecution();

  // 5. Final summary
  console.log('\n🎉 INTEGRATION TEST COMPLETE!');
  console.log('\n📊 Final Status:');
  console.log(`   🔧 Workflows activated: ${activatedCount}`);
  console.log(
    `   🌐 Services connected: ${connectivity.filter((r) => r.success).length}/${connectivity.length}`,
  );
  console.log(
    `   🧪 Test execution: ${testResult.success ? 'SUCCESS' : 'FAILED'}`,
  );

  if (testResult.success) {
    console.log('\n🚀 The integrated SDLC agent system is fully operational!');
    console.log('   📋 Tasks will be automatically discovered and assigned');
    console.log('   🤖 SDLC agents are ready to process work');
    console.log('   🎓 Supervisor is monitoring with internship grading');
  } else {
    console.log('\n⚠️  System is partially operational. Check logs for issues.');
  }
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  activateAllWorkflows,
  testWorkflowConnectivity,
  triggerTestExecution,
};
