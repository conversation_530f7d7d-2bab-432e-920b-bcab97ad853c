#!/usr/bin/env node

/**
 * SDLC Workflow Importer
 * Automatically imports all SDLC agent workflows into n8n on startup
 */

const fs = require('fs');
const path = require('path');
const https = require('https');
const http = require('http');

// Configuration
const N8N_BASE_URL = process.env.N8N_BASE_URL || 'http://localhost:5678';
const WORKFLOWS_DIR = path.join(__dirname, 'workflows');
const MAX_RETRIES = 5;
const RETRY_DELAY = 2000;

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  purple: '\x1b[35m',
  cyan: '\x1b[36m',
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const isHttps = url.startsWith('https');
    const client = isHttps ? https : http;

    const req = client.request(url, options, (res) => {
      let data = '';
      res.on('data', (chunk) => (data += chunk));
      res.on('end', () => {
        try {
          const jsonData = data ? JSON.parse(data) : {};
          resolve({
            statusCode: res.statusCode,
            data: jsonData,
            headers: res.headers,
          });
        } catch (error) {
          resolve({
            statusCode: res.statusCode,
            data: data,
            headers: res.headers,
          });
        }
      });
    });

    req.on('error', reject);

    if (options.body) {
      req.write(options.body);
    }

    req.end();
  });
}

async function waitForN8n() {
  log('⏳ Waiting for n8n to be ready...', 'yellow');

  for (let i = 0; i < MAX_RETRIES; i++) {
    try {
      const response = await makeRequest(`${N8N_BASE_URL}/healthz`);
      if (response.statusCode === 200) {
        log('✅ n8n is ready!', 'green');
        return true;
      }
    } catch (error) {
      // n8n not ready yet
    }

    if (i < MAX_RETRIES - 1) {
      log(
        `   Attempt ${i + 1}/${MAX_RETRIES} failed, retrying in ${RETRY_DELAY / 1000}s...`,
        'yellow',
      );
      await new Promise((resolve) => setTimeout(resolve, RETRY_DELAY));
    }
  }

  throw new Error('n8n failed to become ready');
}

async function getExistingWorkflows() {
  try {
    const response = await makeRequest(`${N8N_BASE_URL}/api/v1/workflows`);
    if (response.statusCode === 200) {
      return response.data.data || [];
    }
    return [];
  } catch (error) {
    log(`⚠️ Could not fetch existing workflows: ${error.message}`, 'yellow');
    return [];
  }
}

async function importWorkflow(workflowFile) {
  const workflowPath = path.join(WORKFLOWS_DIR, workflowFile);

  try {
    // Read workflow JSON
    const workflowData = JSON.parse(fs.readFileSync(workflowPath, 'utf8'));
    const workflowName = workflowData.name;

    log(`📥 Importing: ${workflowName}`, 'blue');

    // Check if workflow already exists
    const existingWorkflows = await getExistingWorkflows();
    const existingWorkflow = existingWorkflows.find(
      (w) => w.name === workflowName,
    );

    if (existingWorkflow) {
      log(
        `   ⚠️ Workflow "${workflowName}" already exists (ID: ${existingWorkflow.id})`,
        'yellow',
      );

      // Update existing workflow
      const updateResponse = await makeRequest(
        `${N8N_BASE_URL}/api/v1/workflows/${existingWorkflow.id}`,
        {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(workflowData),
        },
      );

      if (updateResponse.statusCode === 200) {
        log(`   ✅ Updated workflow: ${workflowName}`, 'green');
        return { action: 'updated', workflow: updateResponse.data };
      }
      log(
        `   ❌ Failed to update workflow: ${updateResponse.statusCode}`,
        'red',
      );
      return { action: 'failed', error: updateResponse.data };
    }
    // Create new workflow
    const createResponse = await makeRequest(
      `${N8N_BASE_URL}/api/v1/workflows`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(workflowData),
      },
    );

    if (
      createResponse.statusCode === 201 ||
      createResponse.statusCode === 200
    ) {
      log(
        `   ✅ Created workflow: ${workflowName} (ID: ${createResponse.data.id})`,
        'green',
      );
      return { action: 'created', workflow: createResponse.data };
    }
    log(`   ❌ Failed to create workflow: ${createResponse.statusCode}`, 'red');
    log(`   Error: ${JSON.stringify(createResponse.data)}`, 'red');
    return { action: 'failed', error: createResponse.data };
  } catch (error) {
    log(`   ❌ Error importing ${workflowFile}: ${error.message}`, 'red');
    return { action: 'failed', error: error.message };
  }
}

async function activateWorkflow(workflowId, workflowName) {
  try {
    const response = await makeRequest(
      `${N8N_BASE_URL}/api/v1/workflows/${workflowId}/activate`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      },
    );

    if (response.statusCode === 200) {
      log(`   ⚡ Activated workflow: ${workflowName}`, 'green');
      return true;
    }
    log(
      `   ⚠️ Could not activate workflow ${workflowName}: ${response.statusCode}`,
      'yellow',
    );
    return false;
  } catch (error) {
    log(
      `   ⚠️ Error activating workflow ${workflowName}: ${error.message}`,
      'yellow',
    );
    return false;
  }
}

async function main() {
  log('🤖 SDLC Workflow Importer', 'purple');
  log('==========================', 'purple');

  try {
    // Wait for n8n to be ready
    await waitForN8n();

    // Check workflows directory
    if (!fs.existsSync(WORKFLOWS_DIR)) {
      log(`❌ Workflows directory not found: ${WORKFLOWS_DIR}`, 'red');
      process.exit(1);
    }

    // Get all workflow files
    const workflowFiles = fs
      .readdirSync(WORKFLOWS_DIR)
      .filter((file) => file.endsWith('.json'))
      .sort(); // Import in order

    if (workflowFiles.length === 0) {
      log('⚠️ No workflow files found', 'yellow');
      process.exit(0);
    }

    log(`📁 Found ${workflowFiles.length} workflow files`, 'blue');

    // Import each workflow
    const results = {
      created: 0,
      updated: 0,
      failed: 0,
      activated: 0,
    };

    for (const workflowFile of workflowFiles) {
      const result = await importWorkflow(workflowFile);

      if (result.action === 'created') {
        results.created++;
        // Try to activate the workflow
        if (await activateWorkflow(result.workflow.id, result.workflow.name)) {
          results.activated++;
        }
      } else if (result.action === 'updated') {
        results.updated++;
        // Try to activate the workflow
        if (await activateWorkflow(result.workflow.id, result.workflow.name)) {
          results.activated++;
        }
      } else {
        results.failed++;
      }

      // Small delay between imports
      await new Promise((resolve) => setTimeout(resolve, 500));
    }

    // Summary
    log('', 'reset');
    log('🎉 SDLC Workflow Import Complete!', 'green');
    log('=================================', 'green');
    log('📊 Results:', 'blue');
    log(`   ✅ Created: ${results.created}`, 'green');
    log(`   🔄 Updated: ${results.updated}`, 'yellow');
    log(`   ❌ Failed: ${results.failed}`, 'red');
    log(`   ⚡ Activated: ${results.activated}`, 'green');
    log('', 'reset');

    if (results.activated > 0) {
      log('🚀 SDLC Agents are now running autonomously!', 'purple');
      log('🔍 Monitor progress at: http://localhost:5678/executions', 'cyan');
      log('📊 View workflows at: http://localhost:5678/workflows', 'cyan');
    }

    process.exit(results.failed > 0 ? 1 : 0);
  } catch (error) {
    log(`❌ Fatal error: ${error.message}`, 'red');
    process.exit(1);
  }
}

// Run the importer
if (require.main === module) {
  main();
}

module.exports = { importWorkflow, waitForN8n };
