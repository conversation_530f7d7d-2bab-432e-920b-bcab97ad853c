#!/usr/bin/env node

const axios = require('axios');

// SDLC Agent Specializations
const SDLC_AGENTS = [
  {
    id: 'agent-frontend-developer-001',
    name: '<PERSON> (Frontend Developer)',
    specialization: 'frontend-developer',
    domain: 'User Interface Development',
    sdlcPhase: 'Development',
    experience: 'intermediate',
    maxComplexity: 3,
    personality: 'creative and detail-oriented',
    strengths: ['React/TypeScript', 'responsive design', 'user experience'],
    weaknesses: ['backend integration', 'performance optimization'],
    webhookPath: 'frontend-agent',
    sprintCapacity: 8, // story points per sprint
    currentSprint: 1,
  },
  {
    id: 'agent-backend-developer-001',
    name: '<PERSON> (Backend Developer)',
    specialization: 'backend-developer',
    domain: 'Server-side Development',
    sdlcPhase: 'Development',
    experience: 'senior',
    maxComplexity: 5,
    personality: 'analytical and systematic',
    strengths: ['API design', 'database optimization', 'security'],
    weaknesses: ['frontend technologies', 'UI/UX design'],
    webhookPath: 'backend-agent',
    sprintCapacity: 12,
    currentSprint: 1,
  },
  {
    id: 'agent-qa-tester-001',
    name: '<PERSON> (QA Engineer)',
    specialization: 'qa-tester',
    domain: 'Quality Assurance',
    sdlcPhase: 'Testing',
    experience: 'intermediate',
    maxComplexity: 3,
    personality: 'meticulous and thorough',
    strengths: ['test automation', 'edge case detection', 'regression testing'],
    weaknesses: ['performance testing', 'security testing'],
    webhookPath: 'qa-agent',
    sprintCapacity: 10,
    currentSprint: 1,
  },
  {
    id: 'agent-devops-engineer-001',
    name: 'Alex Deploy (DevOps Engineer)',
    specialization: 'devops-engineer',
    domain: 'Infrastructure & Deployment',
    sdlcPhase: 'Deployment',
    experience: 'senior',
    maxComplexity: 5,
    personality: 'pragmatic and reliability-focused',
    strengths: ['CI/CD pipelines', 'containerization', 'monitoring'],
    weaknesses: ['application development', 'user interface'],
    webhookPath: 'devops-agent',
    sprintCapacity: 15,
    currentSprint: 1,
  },
  {
    id: 'agent-requirements-analyst-001',
    name: 'Lisa Analyze (Requirements Analyst)',
    specialization: 'requirements-analyst',
    domain: 'Business Analysis',
    sdlcPhase: 'Requirements',
    experience: 'senior',
    maxComplexity: 4,
    personality: 'communicative and detail-oriented',
    strengths: [
      'stakeholder communication',
      'requirement elicitation',
      'documentation',
    ],
    weaknesses: ['technical implementation', 'coding'],
    webhookPath: 'requirements-agent',
    sprintCapacity: 6,
    currentSprint: 1,
  },
  {
    id: 'agent-documentation-specialist-001',
    name: 'David Docs (Documentation Specialist)',
    specialization: 'documentation-specialist',
    domain: 'Technical Writing',
    sdlcPhase: 'Documentation',
    experience: 'intermediate',
    maxComplexity: 2,
    personality: 'clear and methodical',
    strengths: ['technical writing', 'API documentation', 'user guides'],
    weaknesses: ['complex technical concepts', 'code implementation'],
    webhookPath: 'docs-agent',
    sprintCapacity: 5,
    currentSprint: 1,
  },
];

// Create SDLC agents in supervisor system
async function createSDLCAgents() {
  console.log('🏗️  Creating SDLC-based AI Agent Team...\n');

  const createdAgents = [];

  for (const agent of SDLC_AGENTS) {
    try {
      console.log(`👤 Creating: ${agent.name}`);
      console.log(`   🎯 Specialization: ${agent.specialization}`);
      console.log(`   📊 SDLC Phase: ${agent.sdlcPhase}`);
      console.log(`   💪 Experience: ${agent.experience}`);
      console.log(`   🔧 Max Complexity: ${agent.maxComplexity}`);
      console.log(
        `   📈 Sprint Capacity: ${agent.sprintCapacity} story points`,
      );

      // Register agent with supervisor
      const response = await axios.post(
        'http://localhost:3001/webhook/agent-status',
        {
          agentId: agent.id,
          status: 'idle',
          currentTask: null,
          progress: 0,
          lastActivity: new Date().toISOString(),
          agentInfo: {
            ...agent,
            type: 'sdlc-agent',
            createdAt: new Date().toISOString(),
            webhookUrl: `http://localhost:5678/webhook/${agent.webhookPath}`,
          },
        },
      );

      if (response.status === 200) {
        console.log('   ✅ Registered with supervisor');

        // Initialize performance tracking
        await axios.post('http://localhost:3001/webhook/agent-performance', {
          agentId: agent.id,
          taskAssigned: false, // Just initialize
          taskDetails: {
            initialization: true,
            specialization: agent.specialization,
            sprintCapacity: agent.sprintCapacity,
          },
        });

        createdAgents.push(agent);
        console.log('   📊 Performance tracking initialized\n');
      } else {
        console.log('   ❌ Failed to register with supervisor\n');
      }
    } catch (error) {
      console.error(`❌ Failed to create ${agent.name}:`, error.message);
    }
  }

  return createdAgents;
}

// Assign SDLC tasks based on specialization
async function assignSDLCTasks(agents) {
  console.log('📋 Assigning SDLC tasks to specialized agents...\n');

  // Get available tasks from task discovery system
  try {
    const response = await axios.get(
      'http://localhost:3003/api/tasks?completed=false&priority=low',
    );

    if (!response.data.success) {
      console.log('❌ Could not fetch tasks from task discovery system');
      return;
    }

    const availableTasks = response.data.tasks || [];
    console.log(`📊 Found ${availableTasks.length} available tasks`);

    // Assign tasks based on agent specialization
    for (const agent of agents) {
      // Find suitable tasks for this agent's specialization
      const suitableTasks = availableTasks
        .filter((task) => {
          const taskText = task.text.toLowerCase();

          switch (agent.specialization) {
            case 'frontend-developer':
              return (
                taskText.includes('ui') ||
                taskText.includes('frontend') ||
                taskText.includes('component') ||
                taskText.includes('react') ||
                taskText.includes('css') ||
                taskText.includes('responsive')
              );

            case 'backend-developer':
              return (
                taskText.includes('api') ||
                taskText.includes('backend') ||
                taskText.includes('database') ||
                taskText.includes('server') ||
                taskText.includes('endpoint') ||
                taskText.includes('auth')
              );

            case 'qa-tester':
              return (
                taskText.includes('test') ||
                taskText.includes('testing') ||
                taskText.includes('validation') ||
                taskText.includes('quality') ||
                taskText.includes('bug') ||
                taskText.includes('verify')
              );

            case 'devops-engineer':
              return (
                taskText.includes('deploy') ||
                taskText.includes('docker') ||
                taskText.includes('ci/cd') ||
                taskText.includes('infrastructure') ||
                taskText.includes('monitoring') ||
                taskText.includes('pipeline')
              );

            case 'requirements-analyst':
              return (
                taskText.includes('requirement') ||
                taskText.includes('analysis') ||
                taskText.includes('specification') ||
                taskText.includes('business') ||
                taskText.includes('stakeholder') ||
                taskText.includes('user story')
              );

            case 'documentation-specialist':
              return (
                taskText.includes('document') ||
                taskText.includes('readme') ||
                taskText.includes('guide') ||
                taskText.includes('manual') ||
                taskText.includes('comment') ||
                taskText.includes('explain')
              );

            default:
              return false;
          }
        })
        .slice(0, 2); // Limit to 2 tasks per agent

      if (suitableTasks.length > 0) {
        console.log(
          `🎯 Assigning ${suitableTasks.length} tasks to ${agent.name}:`,
        );

        for (const task of suitableTasks) {
          try {
            // Trigger the agent's n8n workflow
            const taskResponse = await axios.post(
              `http://localhost:5678/webhook/${agent.webhookPath}`,
              {
                task: task.text,
                description: `SDLC ${agent.sdlcPhase} task`,
                priority: task.priority || 'medium',
                complexity: task.estimatedComplexity || 2,
                agentId: agent.id.split('-').pop(), // Get the number part
                specialization: agent.specialization,
                sprintCapacity: agent.sprintCapacity,
                taskId: task.id,
              },
            );

            if (taskResponse.status === 200) {
              console.log(`   ✅ Assigned: "${task.text.substring(0, 50)}..."`);
            } else {
              console.log('   ⚠️  Task assignment may have failed');
            }

            // Small delay between assignments
            await new Promise((resolve) => setTimeout(resolve, 1000));
          } catch (error) {
            console.log(`   ❌ Failed to assign task: ${error.message}`);
          }
        }
        console.log('');
      } else {
        console.log(
          `⏭️  No suitable tasks found for ${agent.name} (${agent.specialization})\n`,
        );
      }
    }
  } catch (error) {
    console.error('❌ Error assigning SDLC tasks:', error.message);
  }
}

// Main function
async function main() {
  console.log('🚀 Starting SDLC Agent Team Creation and Task Assignment\n');

  // Create SDLC agents
  const agents = await createSDLCAgents();

  if (agents.length === 0) {
    console.log('❌ No agents were created successfully');
    return;
  }

  console.log(`✅ Successfully created ${agents.length} SDLC agents\n`);

  // Wait for agents to initialize
  console.log('⏳ Waiting for agents to initialize...');
  await new Promise((resolve) => setTimeout(resolve, 3000));

  // Assign tasks
  await assignSDLCTasks(agents);

  console.log('🎉 SDLC Agent Team Setup Complete!\n');
  console.log('📊 Monitor agents at:');
  console.log('   🎓 Supervisor Dashboard: http://localhost:3001');
  console.log('   🔧 n8n Workflows: http://localhost:5678');
  console.log('   📋 Task Discovery: http://localhost:3003');

  console.log('\n👥 SDLC Agent Team:');
  agents.forEach((agent) => {
    console.log(
      `   ${agent.name} - ${agent.sdlcPhase} (${agent.sprintCapacity} SP/sprint)`,
    );
  });
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { SDLC_AGENTS, createSDLCAgents, assignSDLCTasks };
