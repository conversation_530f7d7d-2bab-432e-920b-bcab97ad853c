# 🤖 Autonomous AI Coding Agents for Beauty CRM

> ⚠️ **EXTREME CAUTION REQUIRED** ⚠️
> 
> This system manages 2500+ files with autonomous AI agents. **NEVER run in production without extensive testing and human supervision.** Confidence threshold is set to 0.1 (10%) requiring human approval for most actions.

## 🚨 Critical Warnings

- **SANDBOX ONLY**: Always run in isolated development environment first
- **HUMAN SUPERVISION**: Software architect must supervise all operations
- **BACKUP REQUIRED**: Full git backup before starting any agent operations
- **EMERGENCY STOP**: Always have emergency stop procedures ready
- **LIMITED SCOPE**: Agents should work on specific, well-defined tasks only

## 🏗️ Architecture Overview

```mermaid
graph TB
    subgraph "AI Agent System"
        N8N[n8n Workflow Engine]
        SUPER[Supervisor UI]
        QG[Quality Gate]
        GITOPS[Git Operations]
    end
    
    subgraph "AI Models"
        GEMINI[Gemini 2.5 Flash]
        AUGMENT[Augment Code API]
    end
    
    subgraph "Beauty CRM Workspace"
        FILES[2500+ Files]
        SERVICES[14 Microservices]
        TESTS[Test Suites]
    end
    
    N8N --> GEMINI
    N8N --> AUGMENT
    N8N --> QG
    N8N --> GITOPS
    SUPER --> N8N
    QG --> FILES
    GITOPS --> FILES
    
    FILES --> SERVICES
    FILES --> TESTS
```

## 🔧 Setup Instructions

### 1. Prerequisites

```bash
# Required API Keys
export GEMINI_API_KEY="your_gemini_2_5_api_key"
export AUGMENT_CODE_API_KEY="your_augment_code_api_key"

# Optional but recommended
export OPENAI_API_KEY="your_openai_api_key"
export ANTHROPIC_API_KEY="your_anthropic_api_key"
```

### 2. Environment Setup

```bash
cd shared-product-engineering/autonomous-ai-agents

# Copy and configure environment
cp .env.example .env
# Edit .env with your API keys and settings

# Ensure workspace permissions
chmod -R 755 /private/var/www/2025/ollamar1/beauty-crm
```

### 3. Start the System

```bash
# Start all services
docker compose up -d

# Check service health
docker compose ps
curl http://localhost:5678/health  # n8n
curl http://localhost:3001/health  # supervisor
curl http://localhost:3002/health  # quality gate
```

### 4. Access Interfaces

- **n8n Workflow Designer**: http://n8n.beauty-crm.localhost
- **Supervisor Dashboard**: http://supervisor.beauty-crm.localhost  
- **Quality Gate API**: http://localhost:3002

## 🎯 Agent Configuration

### Confidence Thresholds

- **0.0 - 0.1**: Requires human approval (current setting)
- **0.1 - 0.5**: Automated with quality gate validation
- **0.5 - 1.0**: High confidence, minimal validation

### Risk Levels

- **HIGH**: Database changes, API modifications, deployment configs
- **MEDIUM**: Business logic, component changes, new features  
- **LOW**: Documentation, comments, minor styling

### File Operation Limits

- **Max file size**: 10MB per file
- **Max files per operation**: 50 files
- **Allowed extensions**: `.ts`, `.js`, `.tsx`, `.jsx`, `.json`, `.md`, `.yml`, `.yaml`, `.prisma`

## 🛡️ Safety Mechanisms

### 1. Quality Gate Validation

- **Syntax checking**: TypeScript/JavaScript compilation
- **Linting**: ESLint with strict rules
- **Formatting**: Prettier validation
- **Security scanning**: Basic pattern detection
- **File size limits**: Prevent oversized files

### 2. Human Supervision

- **Real-time monitoring**: Live agent status dashboard
- **Approval workflow**: Human approval for low-confidence actions
- **Emergency stop**: Immediate halt of all agent operations
- **Audit logging**: Complete operation history

### 3. Git Safety

- **Branch protection**: Agents work on feature branches only
- **Commit validation**: Pre-commit hooks with quality checks
- **Rollback capability**: Easy revert of agent changes
- **Change tracking**: Detailed commit messages with agent attribution

## 📋 Workflow Examples

### Basic Code Generation Workflow

1. **Trigger**: GitHub issue or manual request
2. **Analysis**: Agent analyzes requirements using Augment Code
3. **Planning**: Generate implementation plan
4. **Quality Check**: Validate plan against existing codebase
5. **Human Approval**: Request approval if confidence < threshold
6. **Implementation**: Generate code changes
7. **Validation**: Run quality gate checks
8. **Testing**: Execute relevant test suites
9. **Review**: Human review of changes
10. **Commit**: Create feature branch and commit

### Emergency Procedures

```bash
# Emergency stop all agents
curl -X POST http://localhost:3001/api/emergency-stop \
  -H "Content-Type: application/json" \
  -d '{"reason": "Manual intervention required"}'

# Reset to clean state
git stash
git checkout main
git pull origin main

# Restart services
docker-compose restart
```

## 🔍 Monitoring & Debugging

### Log Locations

- **n8n logs**: `docker-compose logs n8n`
- **Supervisor logs**: `docker-compose logs supervisor-ui`
- **Quality gate logs**: `docker-compose logs quality-gate`

### Key Metrics

- **Pending approvals**: Number of actions awaiting human review
- **Active agents**: Currently running agent processes
- **Average confidence**: Mean confidence score of recent actions
- **Risk distribution**: High/medium/low risk action counts

### Debugging Commands

```bash
# Check agent status
curl http://localhost:3001/api/agents

# Validate specific file
curl -X POST http://localhost:3002/validate/file \
  -H "Content-Type: application/json" \
  -d '{"filePath": "services/appointment/src/index.ts"}'

# Full project validation
curl -X POST http://localhost:3002/validate/project
```

## 🚀 Advanced Configuration

### Custom n8n Workflows

1. Import workflow templates from `/workflows` directory
2. Configure AI model connections
3. Set up webhook endpoints
4. Test in sandbox mode

### Agent Specialization

- **Code Generator**: Creates new components and services
- **Refactor Agent**: Improves existing code structure
- **Test Agent**: Generates and maintains test suites
- **Documentation Agent**: Updates README and API docs
- **Security Agent**: Scans for vulnerabilities

## ⚠️ Known Limitations

- **Context window limits**: Large files may exceed AI model limits
- **Dependency conflicts**: Agents may not handle complex dependency trees
- **Integration testing**: Limited ability to test cross-service interactions
- **Performance impact**: Quality validation adds overhead to operations
- **Learning curve**: Requires significant setup and configuration time

## 🆘 Support & Troubleshooting

### Common Issues

1. **Agent stuck in loop**: Use emergency stop and check logs
2. **Quality gate failures**: Review validation rules and file permissions
3. **API rate limits**: Implement backoff strategies in workflows
4. **Memory issues**: Monitor Docker container resource usage

### Getting Help

- Check logs first: `docker-compose logs [service-name]`
- Review n8n workflow execution history
- Validate environment variables and API keys
- Test individual components before full system operation

---

**Remember**: This is experimental technology. Always maintain human oversight and have rollback procedures ready. The system is designed to assist, not replace, human software architects and developers.
