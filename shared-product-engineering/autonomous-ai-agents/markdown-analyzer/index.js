const express = require('express');
const fs = require('fs-extra');
const path = require('path');
const { glob } = require('glob');
const marked = require('marked');
const matter = require('gray-matter');
const chokidar = require('chokidar');
const axios = require('axios');
const { v4: uuidv4 } = require('uuid');
const cron = require('cron');
const simpleGit = require('simple-git');
const helmet = require('helmet');
const cors = require('cors');
const morgan = require('morgan');
require('dotenv').config();

const app = express();
const git = simpleGit(process.env.WORKSPACE_PATH || '/workspace');

// Middleware
app.use(helmet());
app.use(cors());
app.use(morgan('combined'));
app.use(express.json({ limit: '10mb' }));
app.use(express.static(path.join(__dirname, 'public')));

// Configuration
const WORKSPACE_PATH = process.env.WORKSPACE_PATH || '/workspace';
const N8N_WEBHOOK_URL = process.env.N8N_WEBHOOK_URL || 'http://n8n:5678';
const SCAN_INTERVAL = process.env.SCAN_INTERVAL || '0 */30 * * * *'; // Every 30 minutes

// Task storage
const taskDatabase = new Map();
const completedTasks = new Set();

// Task patterns to recognize in markdown
const TASK_PATTERNS = [
  // Checkbox patterns
  /^[\s]*[-*+]?\s*\[[ x]\]\s+(.+)$/gm,
  // TODO patterns
  /^[\s]*[-*+]?\s*TODO:?\s+(.+)$/gim,
  // FIXME patterns
  /^[\s]*[-*+]?\s*FIXME:?\s+(.+)$/gim,
  // Action items
  /^[\s]*[-*+]?\s*Action:?\s+(.+)$/gim,
  // Need to patterns
  /^[\s]*[-*+]?\s*Need to:?\s+(.+)$/gim,
  // Should patterns
  /^[\s]*[-*+]?\s*Should:?\s+(.+)$/gim,
  // Must patterns
  /^[\s]*[-*+]?\s*Must:?\s+(.+)$/gim,
];

// Priority keywords
const PRIORITY_KEYWORDS = {
  high: [
    'urgent',
    'critical',
    'important',
    'asap',
    'priority',
    'breaking',
    'bug',
    'error',
    'fix',
  ],
  medium: ['should', 'improve', 'enhance', 'refactor', 'optimize', 'update'],
  low: ['nice to have', 'consider', 'maybe', 'future', 'eventually', 'cleanup'],
};

// Risk assessment keywords
const RISK_KEYWORDS = {
  high: [
    'database',
    'migration',
    'api',
    'breaking',
    'security',
    'auth',
    'payment',
    'deploy',
  ],
  medium: ['component', 'service', 'logic', 'integration', 'test', 'config'],
  low: ['style', 'comment', 'documentation', 'readme', 'format', 'lint'],
};

// Utility functions
function extractTasksFromMarkdown(content, filePath) {
  const tasks = [];
  const lines = content.split('\n');

  lines.forEach((line, index) => {
    TASK_PATTERNS.forEach((pattern) => {
      const matches = [
        ...line.matchAll(new RegExp(pattern.source, pattern.flags)),
      ];
      matches.forEach((match) => {
        if (match[1] && match[1].trim()) {
          const taskText = match[1].trim();
          const isCompleted = line.includes('[x]') || line.includes('[X]');

          const task = {
            id: uuidv4(),
            text: taskText,
            filePath,
            lineNumber: index + 1,
            isCompleted,
            priority: assessPriority(taskText),
            riskLevel: assessRisk(taskText),
            estimatedComplexity: assessComplexity(taskText),
            tags: extractTags(taskText),
            createdAt: new Date().toISOString(),
            lastModified: new Date().toISOString(),
          };

          tasks.push(task);
        }
      });
    });
  });

  return tasks;
}

function assessPriority(text) {
  const lowerText = text.toLowerCase();

  for (const [priority, keywords] of Object.entries(PRIORITY_KEYWORDS)) {
    if (keywords.some((keyword) => lowerText.includes(keyword))) {
      return priority;
    }
  }

  return 'medium'; // default
}

function assessRisk(text) {
  const lowerText = text.toLowerCase();

  for (const [risk, keywords] of Object.entries(RISK_KEYWORDS)) {
    if (keywords.some((keyword) => lowerText.includes(keyword))) {
      return risk;
    }
  }

  return 'medium'; // default
}

function assessComplexity(text) {
  const lowerText = text.toLowerCase();
  let complexity = 1;

  // Complexity indicators
  if (lowerText.includes('refactor') || lowerText.includes('rewrite'))
    complexity += 3;
  if (lowerText.includes('new') || lowerText.includes('create'))
    complexity += 2;
  if (lowerText.includes('integrate') || lowerText.includes('connect'))
    complexity += 2;
  if (lowerText.includes('test') || lowerText.includes('testing'))
    complexity += 1;
  if (lowerText.includes('fix') || lowerText.includes('bug')) complexity += 1;
  if (lowerText.includes('update') || lowerText.includes('modify'))
    complexity += 1;

  return Math.min(complexity, 5); // Cap at 5
}

function extractTags(text) {
  const tags = [];
  const lowerText = text.toLowerCase();

  // Technology tags
  if (lowerText.includes('typescript') || lowerText.includes('ts'))
    tags.push('typescript');
  if (lowerText.includes('react') || lowerText.includes('tsx'))
    tags.push('react');
  if (lowerText.includes('api') || lowerText.includes('endpoint'))
    tags.push('api');
  if (lowerText.includes('database') || lowerText.includes('db'))
    tags.push('database');
  if (lowerText.includes('test') || lowerText.includes('testing'))
    tags.push('testing');
  if (lowerText.includes('ui') || lowerText.includes('frontend'))
    tags.push('frontend');
  if (lowerText.includes('backend') || lowerText.includes('server'))
    tags.push('backend');
  if (lowerText.includes('docker') || lowerText.includes('container'))
    tags.push('docker');
  if (lowerText.includes('prisma') || lowerText.includes('schema'))
    tags.push('prisma');

  return tags;
}

async function scanMarkdownFiles() {
  console.log('🔍 Scanning markdown files for tasks...');

  try {
    // Find all markdown files
    const markdownFiles = await glob('**/*.md', {
      cwd: WORKSPACE_PATH,
      ignore: ['**/node_modules/**', '**/.git/**', '**/dist/**', '**/build/**'],
    });

    console.log(`Found ${markdownFiles.length} markdown files`);

    let totalTasks = 0;
    let newTasks = 0;

    for (const file of markdownFiles) {
      const filePath = path.join(WORKSPACE_PATH, file);

      try {
        const content = await fs.readFile(filePath, 'utf8');
        const tasks = extractTasksFromMarkdown(content, file);

        tasks.forEach((task) => {
          const existingTask = Array.from(taskDatabase.values()).find(
            (t) =>
              t.filePath === task.filePath && t.lineNumber === task.lineNumber,
          );

          if (!existingTask) {
            taskDatabase.set(task.id, task);
            newTasks++;

            // Notify agents about new task
            notifyAgentsAboutTask(task);
          } else if (existingTask.text !== task.text) {
            // Task was modified
            existingTask.text = task.text;
            existingTask.lastModified = new Date().toISOString();
            existingTask.priority = task.priority;
            existingTask.riskLevel = task.riskLevel;
            existingTask.tags = task.tags;
          }

          totalTasks++;
        });
      } catch (error) {
        console.error(`Error processing ${file}:`, error.message);
      }
    }

    console.log(
      `📊 Scan complete: ${totalTasks} total tasks, ${newTasks} new tasks`,
    );

    return {
      totalFiles: markdownFiles.length,
      totalTasks,
      newTasks,
      tasksByPriority: {
        high: Array.from(taskDatabase.values()).filter(
          (t) => t.priority === 'high',
        ).length,
        medium: Array.from(taskDatabase.values()).filter(
          (t) => t.priority === 'medium',
        ).length,
        low: Array.from(taskDatabase.values()).filter(
          (t) => t.priority === 'low',
        ).length,
      },
      tasksByRisk: {
        high: Array.from(taskDatabase.values()).filter(
          (t) => t.riskLevel === 'high',
        ).length,
        medium: Array.from(taskDatabase.values()).filter(
          (t) => t.riskLevel === 'medium',
        ).length,
        low: Array.from(taskDatabase.values()).filter(
          (t) => t.riskLevel === 'low',
        ).length,
      },
    };
  } catch (error) {
    console.error('Error scanning markdown files:', error);
    throw error;
  }
}

async function notifyAgentsAboutTask(task) {
  try {
    // Send task to n8n workflow for agent assignment
    await axios.post(`${N8N_WEBHOOK_URL}/webhook/new-task-discovered`, {
      taskId: task.id,
      task: task.text,
      description: `Task found in ${task.filePath} at line ${task.lineNumber}`,
      priority: task.priority,
      riskLevel: task.riskLevel,
      complexity: task.estimatedComplexity,
      tags: task.tags,
      filePath: task.filePath,
      lineNumber: task.lineNumber,
      source: 'markdown-analyzer',
    });

    console.log(
      `📤 Notified agents about task: ${task.text.substring(0, 50)}...`,
    );
  } catch (error) {
    console.error('Error notifying agents:', error.message);
  }
}

async function markTaskCompleted(taskId, agentId) {
  const task = taskDatabase.get(taskId);
  if (!task) return false;

  task.isCompleted = true;
  task.completedAt = new Date().toISOString();
  task.completedBy = agentId;

  completedTasks.add(taskId);

  // Update the markdown file to check the checkbox
  try {
    const filePath = path.join(WORKSPACE_PATH, task.filePath);
    const content = await fs.readFile(filePath, 'utf8');
    const lines = content.split('\n');

    if (lines[task.lineNumber - 1]) {
      // Convert [ ] to [x]
      lines[task.lineNumber - 1] = lines[task.lineNumber - 1].replace(
        /\[[ ]\]/,
        '[x]',
      );

      await fs.writeFile(filePath, lines.join('\n'), 'utf8');
      console.log(
        `✅ Marked task completed in ${task.filePath}:${task.lineNumber}`,
      );
    }
  } catch (error) {
    console.error('Error updating markdown file:', error.message);
  }

  return true;
}

// File watcher for real-time updates
const watcher = chokidar.watch(`${WORKSPACE_PATH}/**/*.md`, {
  ignored: /(^|[/\\])\../, // ignore dotfiles
  persistent: true,
  ignoreInitial: true,
});

watcher.on('change', async (filePath) => {
  console.log(`📝 Markdown file changed: ${filePath}`);
  // Re-scan just this file
  try {
    const relativePath = path.relative(WORKSPACE_PATH, filePath);
    const content = await fs.readFile(filePath, 'utf8');
    const tasks = extractTasksFromMarkdown(content, relativePath);

    // Update task database for this file
    // Remove old tasks from this file
    for (const [id, task] of taskDatabase.entries()) {
      if (task.filePath === relativePath) {
        taskDatabase.delete(id);
      }
    }

    // Add new tasks
    tasks.forEach((task) => {
      taskDatabase.set(task.id, task);
      if (!task.isCompleted) {
        notifyAgentsAboutTask(task);
      }
    });
  } catch (error) {
    console.error('Error processing changed file:', error.message);
  }
});

// Scheduled scanning
const scanJob = new cron.CronJob(
  SCAN_INTERVAL,
  async () => {
    console.log('⏰ Scheduled markdown scan starting...');
    await scanMarkdownFiles();
  },
  null,
  true,
);

// API Routes
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    totalTasks: taskDatabase.size,
    completedTasks: completedTasks.size,
    watchedFiles: watcher.getWatched(),
  });
});

app.get('/api/tasks', (req, res) => {
  const { priority, riskLevel, completed, tags } = req.query;

  let tasks = Array.from(taskDatabase.values());

  if (priority) tasks = tasks.filter((t) => t.priority === priority);
  if (riskLevel) tasks = tasks.filter((t) => t.riskLevel === riskLevel);
  if (completed !== undefined)
    tasks = tasks.filter((t) => t.isCompleted === (completed === 'true'));
  if (tags) {
    const tagList = tags.split(',');
    tasks = tasks.filter((t) => tagList.some((tag) => t.tags.includes(tag)));
  }

  res.json({
    success: true,
    count: tasks.length,
    tasks: tasks.sort((a, b) => {
      // Sort by priority (high first), then by creation date
      const priorityOrder = { high: 3, medium: 2, low: 1 };
      if (priorityOrder[a.priority] !== priorityOrder[b.priority]) {
        return priorityOrder[b.priority] - priorityOrder[a.priority];
      }
      return new Date(a.createdAt) - new Date(b.createdAt);
    }),
  });
});

app.post('/api/tasks/scan', async (req, res) => {
  try {
    const results = await scanMarkdownFiles();
    res.json({ success: true, results });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

app.post('/api/tasks/:taskId/complete', async (req, res) => {
  const { taskId } = req.params;
  const { agentId } = req.body;

  try {
    const success = await markTaskCompleted(taskId, agentId);
    if (success) {
      res.json({ success: true, message: 'Task marked as completed' });
    } else {
      res.status(404).json({ success: false, error: 'Task not found' });
    }
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

app.get('/api/stats', (req, res) => {
  const tasks = Array.from(taskDatabase.values());

  res.json({
    success: true,
    stats: {
      total: tasks.length,
      completed: tasks.filter((t) => t.isCompleted).length,
      pending: tasks.filter((t) => !t.isCompleted).length,
      byPriority: {
        high: tasks.filter((t) => t.priority === 'high').length,
        medium: tasks.filter((t) => t.priority === 'medium').length,
        low: tasks.filter((t) => t.priority === 'low').length,
      },
      byRisk: {
        high: tasks.filter((t) => t.riskLevel === 'high').length,
        medium: tasks.filter((t) => t.riskLevel === 'medium').length,
        low: tasks.filter((t) => t.riskLevel === 'low').length,
      },
      byComplexity: {
        1: tasks.filter((t) => t.estimatedComplexity === 1).length,
        2: tasks.filter((t) => t.estimatedComplexity === 2).length,
        3: tasks.filter((t) => t.estimatedComplexity === 3).length,
        4: tasks.filter((t) => t.estimatedComplexity === 4).length,
        5: tasks.filter((t) => t.estimatedComplexity === 5).length,
      },
    },
  });
});

// Initialize
async function initialize() {
  console.log('🚀 Starting Markdown Task Analyzer...');
  console.log(`📁 Workspace: ${WORKSPACE_PATH}`);
  console.log(`🔗 n8n Webhook: ${N8N_WEBHOOK_URL}`);
  console.log(`⏰ Scan Interval: ${SCAN_INTERVAL}`);

  // Initial scan
  await scanMarkdownFiles();

  console.log('✅ Markdown Task Analyzer initialized');
}

// Start server
const PORT = process.env.PORT || 3003;
app.listen(PORT, async () => {
  console.log(`Markdown Task Analyzer running on port ${PORT}`);
  await initialize();
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('Shutting down gracefully...');
  watcher.close();
  scanJob.destroy();
  process.exit(0);
});
