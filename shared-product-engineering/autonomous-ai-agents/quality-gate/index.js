const express = require('express');
const fs = require('fs-extra');
const path = require('path');
const { glob } = require('glob');
const simpleGit = require('simple-git');
const { ESLint } = require('eslint');
const prettier = require('prettier');
const axios = require('axios');
const chokidar = require('chokidar');
const helmet = require('helmet');
const cors = require('cors');
const morgan = require('morgan');
require('dotenv').config();

const app = express();
const git = simpleGit(process.env.WORKSPACE_PATH);

// Middleware
app.use(helmet());
app.use(cors());
app.use(morgan('combined'));
app.use(express.json({ limit: '50mb' }));

// Configuration
const WORKSPACE_PATH = process.env.WORKSPACE_PATH || '/workspace';
const MAX_FILE_SIZE =
  Number.parseInt(process.env.MAX_FILE_SIZE_MB) * 1024 * 1024 || 10485760; // 10MB
const ALLOWED_EXTENSIONS = (
  process.env.ALLOWED_FILE_EXTENSIONS ||
  '.ts,.js,.tsx,.jsx,.json,.md,.yml,.yaml,.prisma'
).split(',');

// Quality checks configuration
const QUALITY_CHECKS = {
  syntax: true,
  linting: true,
  formatting: true,
  testing: true,
  security: true,
  dependencies: true,
  fileSize: true,
  gitStatus: true,
};

// ESLint configuration
const eslint = new ESLint({
  baseConfig: {
    extends: ['eslint:recommended', '@typescript-eslint/recommended'],
    parser: '@typescript-eslint/parser',
    plugins: ['@typescript-eslint'],
    env: { node: true, es2022: true },
    rules: {
      'no-console': 'warn',
      'no-unused-vars': 'error',
      '@typescript-eslint/no-unused-vars': 'error',
      'prefer-const': 'error',
    },
  },
  useEslintrc: false,
});

// File watcher for real-time validation
const watcher = chokidar.watch(WORKSPACE_PATH, {
  ignored: /(^|[/\\])\../, // ignore dotfiles
  persistent: true,
  ignoreInitial: true,
});

watcher.on('change', async (filePath) => {
  if (shouldValidateFile(filePath)) {
    console.log(`File changed: ${filePath}`);
    await validateFile(filePath);
  }
});

// Utility functions
function shouldValidateFile(filePath) {
  const ext = path.extname(filePath);
  return ALLOWED_EXTENSIONS.includes(ext) && !filePath.includes('node_modules');
}

function calculateRiskScore(issues) {
  let score = 0;
  issues.forEach((issue) => {
    switch (issue.severity) {
      case 'error':
        score += 10;
        break;
      case 'warning':
        score += 5;
        break;
      case 'info':
        score += 1;
        break;
    }
  });
  return Math.min(score, 100);
}

// Validation functions
async function validateFile(filePath) {
  const results = {
    filePath,
    timestamp: new Date().toISOString(),
    checks: {},
    issues: [],
    riskScore: 0,
    passed: false,
  };

  try {
    const fileContent = await fs.readFile(filePath, 'utf8');
    const fileStats = await fs.stat(filePath);

    // File size check
    if (QUALITY_CHECKS.fileSize) {
      if (fileStats.size > MAX_FILE_SIZE) {
        results.issues.push({
          type: 'fileSize',
          severity: 'error',
          message: `File size (${fileStats.size} bytes) exceeds maximum allowed (${MAX_FILE_SIZE} bytes)`,
        });
      }
      results.checks.fileSize = fileStats.size <= MAX_FILE_SIZE;
    }

    // Syntax validation for TypeScript/JavaScript files
    if (
      QUALITY_CHECKS.syntax &&
      ['.ts', '.tsx', '.js', '.jsx'].includes(path.extname(filePath))
    ) {
      try {
        require('typescript').transpile(fileContent);
        results.checks.syntax = true;
      } catch (error) {
        results.issues.push({
          type: 'syntax',
          severity: 'error',
          message: `Syntax error: ${error.message}`,
        });
        results.checks.syntax = false;
      }
    }

    // ESLint validation
    if (
      QUALITY_CHECKS.linting &&
      ['.ts', '.tsx', '.js', '.jsx'].includes(path.extname(filePath))
    ) {
      try {
        const lintResults = await eslint.lintText(fileContent, { filePath });
        const messages = lintResults[0]?.messages || [];

        messages.forEach((msg) => {
          results.issues.push({
            type: 'linting',
            severity: msg.severity === 2 ? 'error' : 'warning',
            message: `${msg.message} (${msg.ruleId})`,
            line: msg.line,
            column: msg.column,
          });
        });

        results.checks.linting =
          messages.filter((m) => m.severity === 2).length === 0;
      } catch (error) {
        results.issues.push({
          type: 'linting',
          severity: 'error',
          message: `Linting failed: ${error.message}`,
        });
        results.checks.linting = false;
      }
    }

    // Prettier formatting check
    if (
      QUALITY_CHECKS.formatting &&
      ['.ts', '.tsx', '.js', '.jsx', '.json'].includes(path.extname(filePath))
    ) {
      try {
        const prettierConfig = (await prettier.resolveConfig(filePath)) || {};
        const formatted = await prettier.format(fileContent, {
          ...prettierConfig,
          filepath: filePath,
        });

        results.checks.formatting = fileContent === formatted;
        if (!results.checks.formatting) {
          results.issues.push({
            type: 'formatting',
            severity: 'warning',
            message: 'File is not properly formatted',
          });
        }
      } catch (error) {
        results.issues.push({
          type: 'formatting',
          severity: 'warning',
          message: `Formatting check failed: ${error.message}`,
        });
        results.checks.formatting = false;
      }
    }

    // Security checks (basic)
    if (QUALITY_CHECKS.security) {
      const securityPatterns = [
        {
          pattern: /password\s*=\s*["'][^"']+["']/i,
          message: 'Hardcoded password detected',
        },
        {
          pattern: /api[_-]?key\s*=\s*["'][^"']+["']/i,
          message: 'Hardcoded API key detected',
        },
        {
          pattern: /secret\s*=\s*["'][^"']+["']/i,
          message: 'Hardcoded secret detected',
        },
        { pattern: /eval\s*\(/i, message: 'Use of eval() detected' },
        {
          pattern: /innerHTML\s*=/i,
          message: 'Use of innerHTML detected (XSS risk)',
        },
      ];

      let securityIssues = 0;
      securityPatterns.forEach(({ pattern, message }) => {
        if (pattern.test(fileContent)) {
          results.issues.push({
            type: 'security',
            severity: 'error',
            message,
          });
          securityIssues++;
        }
      });

      results.checks.security = securityIssues === 0;
    }

    // Calculate risk score and overall pass/fail
    results.riskScore = calculateRiskScore(results.issues);
    results.passed =
      results.riskScore < 50 &&
      !results.issues.some((i) => i.severity === 'error');

    return results;
  } catch (error) {
    results.issues.push({
      type: 'system',
      severity: 'error',
      message: `Validation failed: ${error.message}`,
    });
    results.passed = false;
    return results;
  }
}

async function validateProject() {
  const results = {
    timestamp: new Date().toISOString(),
    summary: {
      totalFiles: 0,
      passedFiles: 0,
      failedFiles: 0,
      totalIssues: 0,
      averageRiskScore: 0,
    },
    files: [],
    projectChecks: {},
  };

  try {
    // Find all relevant files
    const patterns = ALLOWED_EXTENSIONS.map((ext) => `**/*${ext}`);
    const files = await glob(patterns, {
      cwd: WORKSPACE_PATH,
      ignore: ['**/node_modules/**', '**/.git/**', '**/dist/**', '**/build/**'],
    });

    console.log(`Validating ${files.length} files...`);

    // Validate each file
    for (const file of files) {
      const filePath = path.join(WORKSPACE_PATH, file);
      const fileResult = await validateFile(filePath);
      results.files.push(fileResult);

      results.summary.totalFiles++;
      if (fileResult.passed) {
        results.summary.passedFiles++;
      } else {
        results.summary.failedFiles++;
      }
      results.summary.totalIssues += fileResult.issues.length;
    }

    // Calculate average risk score
    if (results.files.length > 0) {
      results.summary.averageRiskScore = Math.round(
        results.files.reduce((sum, f) => sum + f.riskScore, 0) /
          results.files.length,
      );
    }

    // Git status check
    if (QUALITY_CHECKS.gitStatus) {
      try {
        const status = await git.status();
        results.projectChecks.gitStatus = {
          clean: status.files.length === 0,
          staged: status.staged.length,
          modified: status.modified.length,
          untracked: status.not_added.length,
        };
      } catch (error) {
        results.projectChecks.gitStatus = { error: error.message };
      }
    }

    // Package.json dependency check
    if (QUALITY_CHECKS.dependencies) {
      try {
        const packageJsonPath = path.join(WORKSPACE_PATH, 'package.json');
        if (await fs.pathExists(packageJsonPath)) {
          const packageJson = await fs.readJson(packageJsonPath);
          const deps = {
            ...packageJson.dependencies,
            ...packageJson.devDependencies,
          };

          results.projectChecks.dependencies = {
            total: Object.keys(deps).length,
            outdated: [], // Would need npm outdated check
            vulnerable: [], // Would need npm audit check
          };
        }
      } catch (error) {
        results.projectChecks.dependencies = { error: error.message };
      }
    }

    return results;
  } catch (error) {
    console.error('Project validation failed:', error);
    return {
      ...results,
      error: error.message,
    };
  }
}

// API Routes
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    workspace: WORKSPACE_PATH,
    checks: QUALITY_CHECKS,
  });
});

app.post('/validate/file', async (req, res) => {
  const { filePath } = req.body;

  if (!filePath) {
    return res.status(400).json({ error: 'filePath is required' });
  }

  const fullPath = path.resolve(WORKSPACE_PATH, filePath);

  if (!fullPath.startsWith(WORKSPACE_PATH)) {
    return res.status(400).json({ error: 'Invalid file path' });
  }

  try {
    const results = await validateFile(fullPath);
    res.json({ success: true, results });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.post('/validate/project', async (req, res) => {
  try {
    const results = await validateProject();
    res.json({ success: true, results });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.post('/webhook/pre-commit', async (req, res) => {
  const { files, agentId } = req.body;

  try {
    const validationResults = [];

    for (const file of files || []) {
      const fullPath = path.resolve(WORKSPACE_PATH, file);
      if (shouldValidateFile(fullPath)) {
        const result = await validateFile(fullPath);
        validationResults.push(result);
      }
    }

    const overallPassed = validationResults.every((r) => r.passed);
    const totalRiskScore = validationResults.reduce(
      (sum, r) => sum + r.riskScore,
      0,
    );

    // Notify n8n workflow
    if (process.env.N8N_WEBHOOK_URL) {
      await axios.post(`${process.env.N8N_WEBHOOK_URL}/quality-gate-result`, {
        agentId,
        passed: overallPassed,
        riskScore: totalRiskScore,
        files: validationResults,
        timestamp: new Date().toISOString(),
      });
    }

    res.json({
      success: true,
      passed: overallPassed,
      riskScore: totalRiskScore,
      files: validationResults,
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Start server
const PORT = process.env.PORT || 3002;
app.listen(PORT, () => {
  console.log(`Quality Gate service running on port ${PORT}`);
  console.log(`Watching workspace: ${WORKSPACE_PATH}`);
  console.log(
    'Enabled checks:',
    Object.keys(QUALITY_CHECKS).filter((k) => QUALITY_CHECKS[k]),
  );
});
