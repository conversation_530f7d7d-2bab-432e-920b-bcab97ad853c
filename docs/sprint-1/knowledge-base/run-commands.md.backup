---
description:
globs:
alwaysApply: false
---
## Standard Run Commands

### Docker Commands

#### Build and Run
```bash
# ✅ DO: Use standardized build commands
docker compose build --no-cache service_name
docker compose up -d service_name

# ❌ DON'T: Use non-standard flags or direct docker build
docker build .
docker compose up
```

#### Development Mode
```bash
# ✅ DO: Use development-specific commands
docker compose -f docker-compose.yml -f docker-compose.dev.yml up -d

# ❌ DON'T: Mix production and development configs
docker compose up -d
```

#### Health Checks
```bash
# ✅ DO: Use health check script
./scripts/docker-healthcheck.sh

# ❌ DON'T: Direct curl commands
curl http://localhost:port/health
```

### Bun Commands

#### Package Installation
```bash
# ✅ DO: Use frozen lockfile
bun install --frozen-lockfile

# ❌ DON'T: Install without lockfile
bun install
```

#### Development Server
```bash
# ✅ DO: Use bun flag
bun --bun run dev

# ❌ DON'T: Run without bun flag
bun run dev
```

#### Production Build
```bash
# ✅ DO: Use build script
bun run build

# ❌ DON'T: Use direct typescript commands
tsc -p tsconfig.json
```

### Standard Scripts in package.json
```json
{
  "scripts": {
    "dev": "bun --bun run src/index.ts",
    "build": "bun build ./src/index.ts --target node",
    "start": "bun --bun run dist/index.js",
    "test": "bun test",
    "docker:build": "docker compose build",
    "docker:up": "docker compose up -d",
    "docker:down": "docker compose down",
    "docker:logs": "docker compose logs -f"
  }
}

## Lessons Learned

### Command Execution Environment
- **Problem**: Commands may behave differently across platforms and environments
- **Impact**: Inconsistent behavior and failed commands
- **Solution**:
  - Always specify full paths in commands
  - Use cross-platform command syntax
  - Document environment requirements
  - Include error handling in scripts

### Package Manager Commands
- **Problem**: Mixed use of package managers can cause confusion
- **Impact**: Inconsistent dependency management and script execution
- **Solution**:
  - Standardize on Bun commands
  - Document command equivalents
  - Use consistent script naming
  - Implement command validation

### Build Tool Commands
- **Problem**: Build tools may require different command formats
- **Impact**: Failed builds and inconsistent outputs
- **Solution**:
  - Document platform-specific command requirements
  - Use cross-platform command syntax
  - Implement command validation
  - Maintain command compatibility documentation

