// Simple icon generator using data URLs
const fs = require('fs');

// Create a simple PNG icon as base64 data URL
function createIconDataURL(size) {
  // This is a simple 1x1 pixel PNG in base64, we'll create a better one
  const canvas = `
    <svg width="${size}" height="${size}" xmlns="http://www.w3.org/2000/svg">
        <defs>
            <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" style="stop-color:#2196F3;stop-opacity:1" />
                <stop offset="100%" style="stop-color:#667eea;stop-opacity:1" />
            </linearGradient>
        </defs>
        <rect width="${size}" height="${size}" rx="${size * 0.15}" fill="url(#grad)"/>
        
        <!-- Cloze exercise lines -->
        <rect x="${size * 0.15}" y="${size * 0.25}" width="${size * 0.05}" height="${size * 0.05}" rx="${size * 0.025}" fill="white"/>
        <rect x="${size * 0.25}" y="${size * 0.25}" width="${size * 0.4}" height="${size * 0.05}" rx="${size * 0.025}" fill="white"/>
        <rect x="${size * 0.7}" y="${size * 0.25}" width="${size * 0.15}" height="${size * 0.05}" rx="${size * 0.025}" fill="white"/>
        
        <rect x="${size * 0.15}" y="${size * 0.4}" width="${size * 0.05}" height="${size * 0.05}" rx="${size * 0.025}" fill="white"/>
        <rect x="${size * 0.25}" y="${size * 0.4}" width="${size * 0.3}" height="${size * 0.05}" rx="${size * 0.025}" fill="white"/>
        <rect x="${size * 0.6}" y="${size * 0.4}" width="${size * 0.25}" height="${size * 0.05}" rx="${size * 0.025}" fill="white"/>
        
        <rect x="${size * 0.15}" y="${size * 0.55}" width="${size * 0.05}" height="${size * 0.05}" rx="${size * 0.025}" fill="white"/>
        <rect x="${size * 0.25}" y="${size * 0.55}" width="${size * 0.35}" height="${size * 0.05}" rx="${size * 0.025}" fill="white"/>
        <rect x="${size * 0.65}" y="${size * 0.55}" width="${size * 0.2}" height="${size * 0.05}" rx="${size * 0.025}" fill="white"/>
        
        <!-- Text -->
        <text x="${size / 2}" y="${size * 0.8}" text-anchor="middle" fill="white" font-family="-apple-system, BlinkMacSystemFont, sans-serif" font-size="${size * 0.08}" font-weight="bold">Dutch</text>
        <text x="${size / 2}" y="${size * 0.9}" text-anchor="middle" fill="white" font-family="-apple-system, BlinkMacSystemFont, sans-serif" font-size="${size * 0.06}">Learning</text>
    </svg>`;

  return 'data:image/svg+xml;base64,' + Buffer.from(canvas).toString('base64');
}

// Generate icons
const sizes = [72, 96, 128, 144, 152, 180, 192, 384, 512];
const icons = [];

sizes.forEach((size) => {
  const dataURL = createIconDataURL(size);
  icons.push({
    src: dataURL,
    sizes: `${size}x${size}`,
    type: 'image/svg+xml',
    purpose: 'any maskable',
  });
});

console.log('Generated icons:', icons.length);
console.log('Icon data URLs created for sizes:', sizes.join(', '));

// Save to a JSON file for easy copying
fs.writeFileSync('icons.json', JSON.stringify(icons, null, 2));
console.log('Icons saved to icons.json');
